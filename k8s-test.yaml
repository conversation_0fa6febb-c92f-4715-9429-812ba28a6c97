apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
  labels:
    app: bananain-polaris-editor-test
  name: bananain-polaris-editor-test
  namespace: bananain-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bananain-polaris-editor-test
  template:
    metadata:
      labels:
        app: bananain-polaris-editor-test
    spec:
      imagePullSecrets:
      - name: my-secret
      containers:
        - name: bananain-polaris-editor-test
          image: 'registry.cn-shenzhen.aliyuncs.com/bananain_web/lowcode-editor:${image_version}'
          ports:
            - containerPort: 80
              protocol: TCP
---
apiVersion: v1
kind: Service
metadata:
  name: bananain-polaris-editor-test-svc
  namespace: bananain-test
spec:
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: bananain-polaris-editor-test
  type: ClusterIP
