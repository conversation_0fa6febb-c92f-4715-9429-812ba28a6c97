apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
  labels:
    app: bananain-polaris-editor-uat
  name: bananain-polaris-editor-uat
  namespace: bananain-uat
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bananain-polaris-editor-uat
  template:
    metadata:
      labels:
        app: bananain-polaris-editor-uat
    spec:
      imagePullSecrets:
      - name: my-secret
      containers:
        - name: bananain-polaris-editor-uat
          image: 'registry.cn-shenzhen.aliyuncs.com/bananain_web/lowcode-editor:${image_version}'
          ports:
            - containerPort: 80
              protocol: TCP
---
apiVersion: v1
kind: Service
metadata:
  name: bananain-polaris-editor-uat-svc
  namespace: bananain-uat
spec:
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: bananain-polaris-editor-uat
  type: ClusterIP
