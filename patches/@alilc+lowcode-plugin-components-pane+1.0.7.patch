diff --git a/node_modules/@alilc/lowcode-plugin-components-pane/es/index.js b/node_modules/@alilc/lowcode-plugin-components-pane/es/index.js
index 18936b9..40c4646 100644
--- a/node_modules/@alilc/lowcode-plugin-components-pane/es/index.js
+++ b/node_modules/@alilc/lowcode-plugin-components-pane/es/index.js
@@ -168,6 +168,10 @@ var ComponentPane = /*#__PURE__*/function (_React$Component) {
   _proto.initComponentList = function initComponentList() {
     var editor = this.props.editor;
     var rawData = isNewEngineVersion ? material.getAssets() : editor.get('assets');
+    console.log('initComponentList--------------------------', rawData)
+    rawData.components = rawData.components.filter(function (item) {
+      return !item.isHidden
+    })
     var meta = transform(rawData, this.t);
     var groups = meta.groups,
       snippets = meta.snippets;
