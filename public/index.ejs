<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>北极星BI编辑器</title>
    <link rel="icon" href="https://bjx.bananain.cn/guandata-store/images/gb1dd5de073a146ed9042809.png">
    <link href="./css/index.css" rel="stylesheet" />
    <!-- 低代码引擎的页面主题样式，可以替换为 theme-lowcode-dark -->
    <link href="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/@alifd/theme-lowcode-light@0.2.1/variables.css" rel="stylesheet" />
    <link href="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/@alifd/theme-lowcode-light@0.2.1/next.var.min.css" rel="stylesheet" />
    <!-- 低代码引擎的页面框架样式 -->
    <link rel="stylesheet" href="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/@alilc/lowcode-engine/1.1.6/engine-core.css" />
    <!-- 低代码引擎官方扩展的样式 -->
    <link rel="stylesheet" href="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/@alilc/lowcode-engine-ext/1.0.6-beta.23/engine-ext.css" />
    <link rel="stylesheet" href="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/antd/version/4.24.13/antd.min.css" />

    <!-- React，可替换为 production 包 -->
    <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/react/16.14.0/react.production.min.js"></script>
    <!-- React DOM，可替换为 production 包 -->
    <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/react-dom/16.14.0/react-dom.production.min.js"></script>
    <!-- React 向下兼容，预防物料层的依赖 -->
    <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/prop-types/15.7.2/prop-types.js"></script>
    <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/react15-polyfill/0.0.1/index.js"></script>
    <!-- lodash，低代码编辑器的依赖 -->
    <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/lodash/4.6.1/lodash.min.js"></script>
    <!-- 日期处理包，Fusion Next 的依赖 -->
    <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/moment/2.24.0/moment.min.js"></script>
    <!-- Fusion Next 的主包，低代码编辑器的依赖 -->
    <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/@alifd/next/1.23.24/next.min.js"></script>
    <!-- 低代码引擎的主包 -->
    <script crossorigin="anonymous" src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/@alilc/lowcode-engine/1.1.8/engine-core.js"></script>
    <!-- 低代码引擎官方扩展的主包 -->
    <script crossorigin="anonymous" src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/@alilc/lowcode-engine-ext/1.0.6-beta.23/engine-ext.js"></script>
    <script crossorigin="anonymous" src=" https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/crypto-js/4.0.0/crypto-js.min.js"></script>
    
    <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/dayjs/1.11.10/dayjs.min.js"></script>
    <script>
      window.ENV = '<%= ENV %>';
      console.log(
        '%c AliLowCodeEngineDemo %c v<%= version %> ',
        'padding: 2px 1px; border-radius: 3px 0 0 3px; color: #fff; background: #b37feb; font-weight: bold;',
        'padding: 2px 1px; border-radius: 0 3px 3px 0; color: #fff; background: #42c02e; font-weight: bold;',
      );

    </script>
  </head>
  <style>
    #app{
      font-size: 50px;
    }
  </style>
  <body>
    <div id="lce-container"></div>
    <script type="text/javascript" src="./js/index.js"></script>
  </body>
</html>
