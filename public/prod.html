<!--
 * @Author: kiki
 * @Date: 2023-09-13 18:38:19
 * @LastEditTime: 2023-09-18 17:38:43
 * @LastEditors: kiki
 * @Description: 
-->
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>北极星</title>
  <link rel="icon" href="https://bjx.bananain.cn/guandata-store/images/gb1dd5de073a146ed9042809.png">
  <link href="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/@alifd/next/1.23.24/next.min.css" rel="stylesheet">
  <link href="./css/preview.css" rel="stylesheet">
  <style>
    body {
      background-color: transparent !important;
    }
  </style>
  <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/react/16.14.0/react.production.min.js"></script>
  <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/react-dom/16.14.0/react-dom.production.min.js"></script>
  <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/prop-types/15.7.2/prop-types.js"></script>
  <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/react15-polyfill/0.0.1/polyfill-mixed-lodash.js"></script>
  <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/moment/2.24.0/moment.min.js"></script>
  <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/@alifd/next/1.26.4/next.min.js"></script>
  <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/echarts/5.4.3/echarts.min.js"></script>
  <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/dayjs/1.11.10/dayjs.min.js"></script>
  <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/lodash/4.6.1/lodash.min.js"></script>
  <script src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/bizcharts/4.1.23/BizCharts.min.js"></script>
  <script crossorigin="anonymous" src=" https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/crypto-js/4.0.0/crypto-js.min.js"></script>
</head>
<body>
<div id="ice-container"></div>

<script>
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

  window.ENV = '<%=htmlWebpackPlugin.options.templateParameters.ENV %>';
  window.onClient = true;
// 移除SSO相关配置
// 由于全量放开影响太大， 先进行页面级别的权限处理
  // window.isUseSelfToken = true
</script>



<script type="text/javascript" src="./js/prod.js"></script>
     
</script>
</body>
</html>
