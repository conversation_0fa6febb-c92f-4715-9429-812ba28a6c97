# cat nginx.conf
worker_processes auto;
events {
    worker_connections 1024;
}
http {
    include mime.types;
    default_type application/octet-stream;
    sendfile on;
    keepalive_timeout 65;
    client_max_body_size 20m;
    server {
        listen 80;
        server_name localhost;
        location /lowcode-editor {
            root /usr/share/nginx/html;
            index  index.html index.htm;
            try_files $uri $uri/ /lowcode-editor/index.html;
            # 设置htmll类型 no-store 缓存策略
            if ($request_filename ~* ^.*?.(html|htm)$) {
                add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
            }
        }
        location  ^~ /pages/ {
            proxy_pass https://service-f8ytpbol-1254514826.sh.apigw.tencentcs.com/release/;
        }
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root html;
        }
    }
}
