const fs = require('fs-extra');
const webpack = require('webpack');
const TsconfigPathsPlugin = require('tsconfig-paths-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const { version } = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
const { REACT_APP_ENV = 'dev' } = process.env;

module.exports = ({ onGetWebpackConfig }) => {
  onGetWebpackConfig((config) => {
    config.resolve.plugin('tsconfigpaths').use(TsconfigPathsPlugin, [
      {
        configFile: './tsconfig.json',
      },
    ]);

    config.merge({
      mode: 'development',
      node: {
        fs: 'empty',
      },
      devServer: {
        proxy: {
          '/api/card': {
            target: 'https://bjx.bananain.cn',
            changeOrigin: true,
            // pathRewrite: { '^/api': '' },
          },
          '/pages': {
            target: 'https://service-f8ytpbol-1254514826.sh.apigw.tencentcs.com/release',
            changeOrigin: true,
            pathRewrite: { '^/pages': '' },
          },
          '/bjx-app-api': {
            // 移除SSO相关注释
            // target: 'https://dev-sso.bananain.cn:3443',// 开发环境
            // target: 'https://test-bi.bananain.cn',// 测试环境
            target: 'https://uat.bananain.cn', // 测试环境
            // target: 'https://bjx.bananain.cn/',
            changeOrigin: true,
            // pathRewrite: { '^/api': '' },
          },
          // 移除auth-app-api代理配置，不再需要SSO认证
          // '/auth-app-api': {
          //   target: 'https://uat.bananain.cn',// 测试环境
          //   // target: 'https://test-bi.bananain.cn',
          //   // target: 'https://bjx.bananain.cn/',
          //   changeOrigin: true,
          //   pathRewrite: { '^': '' },
          // },
          '/index-app-api': {
            // target: 'https://test-bi.bananain.cn',
            // target: 'https://bjx.bananain.cn/',
            target: 'https://uat.bananain.cn', // 测试环境
            changeOrigin: true,
            pathRewrite: { '^': '' },
          },
          // 移除SSO相关的common-app-api代理配置
          // '/common-app-api/': {
          //   // target: 'https://uat.bananain.cn',
          //   target: 'https://test-sso.bananain.cn/',
          //   // target: 'http://192.168.30.82:10501',
          //   // target: 'https://bjx.bananain.cn/',
          //   changeOrigin: true,
          //   pathRewrite: { '^': '' },
          // },
          '/voc-api': {
            target: 'https://test-bi.bananain.cn',
            changeOrigin: true,
            pathRewrite: { '^': '' },
          },
        },
      },
      externals: {
        react: 'var window.React',
        'react-dom': 'var window.ReactDOM',
        'prop-types': 'var window.PropTypes',
        '@alifd/next': 'var window.Next',
        jiaoneiui: 'var window.Jiaoneiui',
        '@alilc/lowcode-engine': 'var window.AliLowCodeEngine',
        '@alilc/lowcode-engine-ext': 'var window.AliLowCodeEngineExt',
        moment: 'var window.moment',
        dayjs: 'var window.dayjs',
        echarts: 'var window.echarts',
        lodash: 'var window._',
      },
    });

    config.plugin('index').use(HtmlWebpackPlugin, [
      {
        inject: false,
        minify: false,
        templateParameters: {
          ENV: REACT_APP_ENV,
          version,
        },
        template: require.resolve('./public/index.ejs'),
        filename: 'index.html',
      },
    ]);
    config.plugin('preview').use(HtmlWebpackPlugin, [
      {
        inject: false,
        templateParameters: {
          ENV: REACT_APP_ENV,
        },
        template: require.resolve('./public/preview.html'),
        filename: 'preview.html',
      },
    ]);
    config.plugin('prod').use(HtmlWebpackPlugin, [
      {
        inject: false,
        templateParameters: {
          ENV: REACT_APP_ENV,
        },
        template: require.resolve('./public/prod.html'),
        filename: 'prod.html',
      },
    ]);
    config.plugin('editor').use(HtmlWebpackPlugin, [
      {
        inject: false,
        templateParameters: {
          ENV: REACT_APP_ENV,
        },
        template: require.resolve('./public/editor.html'),
        filename: 'editor.html',
      },
    ]);
    config.plugins.delete('hot');

    config.devServer.hot(false);
    config.module // fixes https://github.com/graphql/graphql-js/issues/1272
      .rule('mjs$')
      .test(/\.mjs$/)
      .include.add(/node_modules/)
      .end()
      .type('javascript/auto');
  });
};
