apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
  labels:
    app: bananain-polaris-editor-dev
  name: bananain-polaris-editor-dev
  namespace: bananain-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bananain-polaris-editor-dev
  template:
    metadata:
      labels:
        app: bananain-polaris-editor-dev
    spec:
      imagePullSecrets:
      - name: my-secret
      containers:
        - name: bananain-polaris-editor-dev
          image: 'registry.cn-shenzhen.aliyuncs.com/bananain_web/lowcode-editor:${image_version}'
          ports:
            - containerPort: 80
              protocol: TCP
---
apiVersion: v1
kind: Service
metadata:
  name: bananain-polaris-editor-dev-svc
  namespace: bananain-dev
spec:
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: bananain-polaris-editor-dev
  type: ClusterIP
