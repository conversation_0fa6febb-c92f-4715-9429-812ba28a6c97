/*
 * @Author: kiki
 * @Date: 2023-09-13 18:38:46
 * @LastEditTime: 2023-11-23 13:43:31
 * @LastEditors: kiki
 * @Description:
 */
declare global {
  interface Window {
    pageConfig?: any;
    dict?: any;
    filters?: any;
    ENV?: 'dev' | 'test' | 'uat' | 'prod';
    JNTools?: any;
    onClient?: boolean;
    // 移除SSO相关字段
    // isUseSelfToken?: boolean;
    hasCipherKey?: boolean;
    isMobile?: boolean;
    JNGlobalEvent: any;
  }
}

interface Window {
  pageConfig?: any;
  dict?: any;
  filters?: any;
  ENV?: 'dev' | 'test' | 'uat' | 'prod';
  JNTools?: any;
  onClient?: boolean;
  // 移除SSO相关字段
  // isUseSelfToken?: boolean;
  hasCipherKey?: boolean;
  isMobile?: boolean;
  JNGlobalEvent: any;
  pageView?: PageView;
  pageViewId?: string | number;
}

// lowcode 相关库的声明文件, 解决eslint报错
declare module '@alilc/lowcode-engine';
