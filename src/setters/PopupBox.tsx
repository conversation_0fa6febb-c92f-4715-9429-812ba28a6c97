import * as React from "react";
import { AddJump ,setConfig} from "src/material/popupConfig/components/AddJump";
import { Button, Dialog } from '@alifd/next';
import { IPublicModelNode } from "@alilc/lowcode-types";


interface PopupBoxSetterProps {
  // 当前值
  value: string;
  defaultValue: any
  selected: IPublicModelNode
  // 默认值
  initialValue: string;
  // setter 唯一输出
  onChange: (val: any) => void;
  // AltStringSetter 特殊配置
  placeholder: string;
  onOpen: () => void;
  onOk: () => void;
  onCancel: () => void;
  
}
export default class PopupBoxSetter extends React.PureComponent<PopupBoxSetterProps> {
  onOk: (e: any) => void;
  onOpen: (e: any) => void;
  onClose: (triggerType:any, e: any) => void; 

  public state: {
    visible: boolean;
  };
  constructor(props:any) {

    super(props);
    this.state = {
      visible: false,
    };
    this.onOpen = () => {
      this.setState({
        visible: true,
      });
    };
    this.onOk = e => {
      console.log(e);
      const tempConfig = setConfig();
      this.props.onChange(null);
      this.setState({
        visible: false,
      },()=>{
        this.props.onChange(tempConfig);
      });
      
    };
    this.onClose = (triggerType, e) => {
      console.log(triggerType, e);
      this.setState({
        visible: false,
      });
    };
  }

  // 声明 Setter 的 title
  static displayName = 'PopupBoxSetter';
  render() {
    const { selected, value } = this.props;
    const { visible } = this.state;
    return (
     <div >
        <Button onClick={this.onOpen} type="primary" size="small">
          浮窗配置
        </Button>
        <Dialog
          v2
          
          title="创建浮窗"
          visible={this.state.visible}
          wrapperClassName="popupDialog"
          onOk={this.onOk}
          onClose={this.onClose}
          onCancel={this.onClose}
        >
         <AddJump node={selected} isSetter={true} defaultValue={value}/>
        </Dialog>
      </div>
    );
  }
}