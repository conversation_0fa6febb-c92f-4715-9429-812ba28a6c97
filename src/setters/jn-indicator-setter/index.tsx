import React, { PureComponent } from 'react';
import { Select } from '@alifd/next';
import _ from 'lodash';
import axios from 'axios';

interface SelectSetterState {
    setterValue: string | null;
    options: any[];
}

interface SelectSetterProps {
    onChange: (value: string) => undefined;
    value?: any;
    defaultValue?: any;
    options: any[];
    /**
     * 展开后是否能搜索
     */
    filterType?: string;
}

// 指标下拉setter
export default class JNDicatorSetter extends PureComponent<SelectSetterProps, SelectSetterState> {
    static defaultProps = {
        placeholder: '请选择',
        options: [{ label: '-', value: '' }],
        defaultValue: null as any,
        onChange: () => undefined as any,
    };

    static displayName = 'JNSelectSetter';

    state: SelectSetterState = {
        setterValue: null,
        options: [],
    };

    bindEvent = async () => {
        const tempOptions = window.jnIndicatorList || [];

        this.setState((state) => {
            return { ...state, options: tempOptions };
        })
    }

    componentDidMount() {
        this.bindEvent();
    }

    render() {
        const { onChange, value } = this.props;

        return (
            <Select
                autoWidth={false}
                useVirtual
                hasClear
                size="small"
                value={value}
                dataSource={this.state.options}
                mode='single'
                onChange={(val) => {
                    onChange && onChange(val);
                }}
                style={{ width: '100%' }}
                showSearch={true}
            />
        );
    }
}
