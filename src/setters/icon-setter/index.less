.lc-icon-setter {
  .next-input {
    width: 100% !important;
  }
  i {
    min-width: 12px;
  }
  i:hover {
    cursor: pointer;
  }
  input:hover {
    cursor: pointer;
  }
}

.lowcode-icon-setter-popup {
  max-width: 354px;
  max-height: 80vh;
  overflow-x: hidden;
  overflow-y: auto;
  top: 48px !important;
  z-index: 8888 !important;
  .lowcode-icon-list {
    overflow: auto;
    li {
      float: left;
      margin-bottom: 10px;
      width: 40px;
      color: #666;
      cursor: pointer;
      text-align: center;

      &:hover {
        color: #000;
      }
    }
  }
}
