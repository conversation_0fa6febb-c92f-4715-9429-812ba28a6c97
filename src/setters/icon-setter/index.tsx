import React, { PureComponent } from 'react';
// import PropTypes from 'prop-types';
import { Input, Icon, Balloon } from '@alifd/next';
import IconFont from './IconFont';
import iconData from './icon.json';
import iconData2 from './icon-2.json';

import './index.less';

const handleData = () => {
  const tempIcons = [...iconData.glyphs, ...iconData2.glyphs].map(item => `icon-${item.font_class}`);
  return tempIcons;
}

const icons = handleData();
interface IconSetterProps {
  value: string;
  type: string;
  defaultValue: string;
  placeholder: string;
  hasClear: boolean;
  align: 'l' | 'r';
  onChange: (icon: string) => undefined;
  icons: string[];
}
interface IconSetterState {
  setterValue: string | object | null;
}

export default class IconFontSetter extends PureComponent<IconSetterProps, IconSetterState> {
  static defaultProps = {
    value: undefined,
    type: 'string',
    defaultValue: '',
    hasClear: true,
    align: 'l',
    icons,
    placeholder: '请点击选择 Icon',
    onChange: () => undefined,
  };
  static displayName = 'IconSetter';

  static getDerivedStateFromProps(nextProps: any, prevState: any): any {
    const { value, defaultValue } = nextProps;
    if (prevState.setterValue == null) {
      if (value === undefined && defaultValue) {
        return {
          setterValue: defaultValue,
        };
      }
    }

    return {
      setterValue: value,
    };
  }

  state = {
    setterValue: null,
  };

  _onChange = (icon: string) => {
    const { onChange, type } = this.props;
    if (type === 'string') {
      onChange(icon);
    } else if (type === 'node') {
      onChange({
        componentName: 'Icon',
        props: {
          type: icon,
        },
      });
    }
  };

  onInputChange = (icon: string) => {
    this._onChange(icon);
  };

  onSelectIcon = (icon: string) => {
    this._onChange(icon);
  };

  render() {
    const { placeholder, hasClear, align } = this.props;
    const { setterValue } = this.state;
    const _value = typeof setterValue === 'object' ? setterValue?.props?.type : setterValue;
    const currentIcon = <IconFont style={{ fontSize: '24px' }} size="xs" type={_value} />;
    const clearIcon = hasClear && (
      <Icon
        style={{ fontSize: '24px' }}
        size="xs"
        id="icon-clear"
        type="delete-filling"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          this.onSelectIcon('');
        }}
      />
    );

    const triggerNode = (
      <div>
        <Input
          size="small"
          placeholder={placeholder}
          addonTextBefore={currentIcon}
          onChange={this.onInputChange}
          value={_value}
          readOnly
          addonTextAfter={clearIcon}
        />
      </div>
    );
    const InnerBeforeNode = (
      <Balloon
        className={'lowcode-icon-content'}
        trigger={triggerNode}
        needAdjust
        shouldUpdatePosition
        triggerType="click"
        closable={false}
        alignEdge
        align={align || 'l'}
        popupClassName="lowcode-icon-setter-popup"
      >
        <ul className="lowcode-icon-list">
          {icons.map((icon) => (
            <li key={icon} onClick={() => this.onSelectIcon(icon)}>
              <IconFont style={{ fontSize: '24px' }} type={icon} size="medium" />
            </li>
          ))}
        </ul>
      </Balloon>
    );

    return <div className="lc-icon-setter">{InnerBeforeNode}</div>;
  }
}
