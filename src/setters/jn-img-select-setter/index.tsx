/*
 * @Author: kiki
 * @Date: 2024-06-18 13:28:43
 * @LastEditTime: 2024-07-05 15:01:20
 * @LastEditors: kiki
 * @Description: 
 */
import * as React from "react";
import { Button, Balloon, Icon } from "@alifd/next";

import "./index.less";

const ICONS_PATH = "https://cdn-h5.bananain.cn/icons";
const IMG_MAP = [
  `${ICONS_PATH}/icon-indicator-efficiency.png`,
  `${ICONS_PATH}/icon-indicator-project.png`,
  `${ICONS_PATH}/icon-indicator-cost.png`,
  `${ICONS_PATH}/icon-indicator-develop.png`,
  `${ICONS_PATH}/icon-indicator-quality.png`,
  `${ICONS_PATH}/icon-indicator-commodity.png`,
  `${ICONS_PATH}/icon-indicator-experience.png`,
  `${ICONS_PATH}/icon-indicator-profit.png`,
  `${ICONS_PATH}/icon-indicator-put-in.png`,
  `${ICONS_PATH}/icon-indicator-sale.png`,
]

interface JNImgSelectSetterProps {
  // 当前值
  value: string;
  // 默认值
  initialValue: string;
  // setter 唯一输出
  onChange: (val: string) => void;
  // JNImgSelectSetter 特殊配置
  placeholder: string;
}
export default class JNImgSelectSetter extends React.PureComponent<JNImgSelectSetterProps> {
  componentDidMount() {
    const { onChange, value, defaultValue } = this.props;
    if (value == undefined && defaultValue) {
      onChange(defaultValue);
    }
  }

  // 声明 Setter 的 title
    static displayName = 'JNImgSelectSetter';

  render() {
    const { onChange, value } = this.props;

    const triggerNode = (
        value ? (
            <div className="lowcode-imgselect-setter-iamge-wrapper">
                <img src={value} alt="" className="lowcode-imgselect-setter-iamge-img" />
                <Icon
                  size="xs"
                  id="icon-clear"
                  type="delete-filling"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onChange('');
                  }}
                />
                <div className="lowcode-imgselect-setter-iamge-clear"></div>
            </div>
        ) : (
            <Button style={{ fontSize: '12px'}}>选择图片</Button>
        )
    );

    return (
        <Balloon
            trigger={triggerNode}
            needAdjust
            shouldUpdatePosition
            triggerType="click"
            closable={false}
            alignEdge
            align='l'
            popupClassName="lowcode-imgselect-setter-popup"
        >
          <div className="lowcode-imgselect-setter-content-title">图片&ensp;icon&ensp;库</div>
          <ul className="lowcode-imgselect-setter-content">
            {IMG_MAP.map((url, index) => (
              <li
                key={index}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onChange(url);     
                }}
                >
                  <img src={url} alt="" />
                </li>
            ))}
          </ul>
        </Balloon>
    );
  }
}