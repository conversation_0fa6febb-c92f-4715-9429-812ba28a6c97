

.lowcode-imgselect-setter-popup {
    width: 354px;
    min-height: 50vh;
    max-height: 80vh;
    overflow-x: hidden;
    overflow-y: auto;
    top: 48px !important;
    z-index: 8888 !important;
    .lowcode-imgselect-setter-content {
      overflow: auto;
      li {
        display: inline-block;
        margin: 0 6px 10px;
        height: 40px;
        width: 40px;
        color: #666;
        cursor: pointer;
        text-align: center;
        >img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
  
        &:hover {
          color: #000;
        }
      }
    }
  }

  .lowcode-imgselect-setter-iamge-wrapper {
      position: relative;
      display: inline-block;
      >img {
          display: block;
          height: 40px;
          width: 40px;
          object-fit: contain;
      }
      >i {
          position: absolute;
          font-size: 16px;
          right: -18px;
            top: -6px;
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          cursor: pointer;
      }
  }
  
  .lowcode-imgselect-setter-content-title {
    font-size: 16px;
    font-weight: 600;
    padding-bottom: 10px;
    text-align: center;
    border-bottom: 1px solid #dddddd;
    margin-bottom: 10px;
  }