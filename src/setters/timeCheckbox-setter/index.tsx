import * as React from 'react';
import { Checkbox } from 'antd';
const CheckboxGroup = Checkbox.Group;
interface CheckboxGroupSetterProps {
  value: string[];
  disabled: boolean;
  options: any;
  defaultValue: any;
  onChange: (val: string) => void;
}
interface CheckboxGroupSetterState {
  setterValue: string | null;
}
export default class CheckboxGroupSetter extends React.PureComponent<
  CheckboxGroupSetterProps,
  CheckboxGroupSetterState
> {
  static displayName = 'CheckboxGroupSetter';

  state: CheckboxGroupSetterState = {
    setterValue: null,
  };

  // componentDidMount() {
  //   const { onChange, } = this.props;
  //   onChange(['昨天', '上周', '本月至昨天', '日', '周', '月', '年', '自定义']);
  // }

  render() {
    const { onChange, value } = this.props;
    const dataSource = [
      '双11备货',
      '双11预售起',
      '双11正式起',
      '双12备货',
      '昨天',
      '今日',
      '上周',
      '本周至昨天',
      '本月至昨天',
      '本年至昨天',
      '预售至昨天',
      '正式至昨天',
      '未来7天',
      '未来30天',
      '未来90天',
      '未来180天',
      '日',
      '周',
      '月',
      '年',
      '自定义',
      '近7天',
      '近30天',
      '全部'
    ];
    const defaultValue = ['昨天', '上周', '本月至昨天', '日', '周', '月', '年', '自定义'];
    return (
      <div className="Checkboxgroup-style">
        <CheckboxGroup
          options={dataSource}
          onChange={(val: any) => onChange(val)}
          value={value}
          defaultValue={defaultValue}
        />
      </div>
    );
  }
}
