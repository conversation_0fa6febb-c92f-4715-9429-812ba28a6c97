import React, { PureComponent } from 'react';
import { Select } from '@alifd/next';
import { event } from '@alilc/lowcode-engine';
import _ from 'lodash';

interface SelectSetterProps {
    onChange: (value: string) => undefined;
    value?: any;
    mode?: 'single' | 'multiple' | 'tag';
    defaultValue?: any;
    options: any[];
    /**
     * 展开后是否能搜索
     */
    showSearch?: boolean;
    filterType?: string;
}

interface SelectSetterState {
    setterValue: string | null;
    options: any[];
}

export default class JNSelectSetter extends PureComponent<SelectSetterProps, SelectSetterState> {

    static defaultProps = {
        placeholder: '请选择',
        options: [{ label: '-', value: '' }],
        defaultValue: null as any,
        onChange: () => undefined as any,
    };

    static displayName = 'JNSelectSetter';

    state: SelectSetterState = {
        setterValue: null,
        options: [],
    };

    bindEvent = (value: any, filter: string | string[]) => {
        let tempOptions: { label: string; value: any; }[] = []
        if (window?.dict) {
            const { list } = window?.dict?.pageDataList
            const datasetCategoryId = value;
            if (_.isArray(filter)) {
                tempOptions = _.chain(list).filter((item) => (filter.includes(item?.viewType)) || !filter)
                    .filter((newItem => datasetCategoryId ? newItem.datasetCategoryId == datasetCategoryId : true)).map((item => {
                        return {
                            label: `${item.datasetName}[${item.id}]`,
                            value: item.id
                        }
                    })).value()

            } else {
                tempOptions = _.chain(list).filter((item) => item?.viewType === filter || !filter)
                    .filter((newItem => datasetCategoryId ? newItem.datasetCategoryId == datasetCategoryId : true)).map((item => {
                        return {
                            label: `${item.datasetName}[${item.id}]`,
                            value: item.id
                        }
                    })).value()
            }
        }
        this.setState((state) => {
            return { ...state, options: tempOptions };
        })
    }

    componentDidMount() {
        const { filterType } = this.props;
        const datasetCategoryId = localStorage.getItem('datasetCategoryID');
        this.bindEvent(datasetCategoryId, filterType);

        event.on(`common:JNSelectSetter.bindEvent`, (value) => this.bindEvent(value, filterType));
    }

    componentWillUnmount() {
        const { filterType } = this.props;
        event.off(`common:JNSelectSetter.bindEvent`, (value) => this.bindEvent(value, filterType));
    }


    render() {
        const { onChange, mode, value, showSearch } = this.props;
        return (
            <Select
                autoWidth={false}
                size="small"
                value={value}
                dataSource={this.state.options}
                mode={mode}
                onChange={(val) => {
                    onChange && onChange(val);
                }}
                style={{ width: '100%' }}
                showSearch={showSearch}
            />
        );
    }
}
