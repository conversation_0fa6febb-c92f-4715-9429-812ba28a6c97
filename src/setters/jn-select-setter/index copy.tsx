import React, { PureComponent, useEffect, useState, } from 'react';
import { Select } from '@alifd/next';
import { event } from '@alilc/lowcode-engine';
import _ from 'lodash';

interface SelectSetterProps {
    onChange: (value: string) => undefined;
    value?: any;
    mode?: 'single' | 'multiple' | 'tag';
    defaultValue?: any;
    options: any[];
    /**
     * 展开后是否能搜索
     */
    showSearch?: boolean;
    filterType?: string;
}

const SETTER_NAME = '';

const JNSelectSetter = (props: SelectSetterProps) => {
    const { onChange, mode, value, showSearch, filterType } = props;
    const [options, setOptions] = useState([]);

    const bindEvent = (value: any, filter: string | string[]) => {
        let tempOptions: { label: string; value: any; }[] = []
        if (window?.dict) {
            const { list } = window?.dict?.pageDataList
            const datasetCategoryId = value;
            if (_.isArray(filter)) {
                tempOptions = _.chain(list).filter((item) => (filter.includes(item?.viewType)) || !filter)
                    .filter((newItem => datasetCategoryId ? newItem.datasetCategoryId == datasetCategoryId : true)).map((item => {
                        return {
                            label: `${item.datasetName}[${item.id}]`,
                            value: item.id
                        }
                    })).value()

            } else {
                tempOptions = _.chain(list).filter((item) => item?.viewType === filter || !filter)
                    .filter((newItem => datasetCategoryId ? newItem.datasetCategoryId == datasetCategoryId : true)).map((item => {
                        return {
                            label: `${item.datasetName}[${item.id}]`,
                            value: item.id
                        }
                    })).value()
            }
        }
        setOptions(tempOptions);
    }

    useEffect(() => {
        // 因为第一次进入无法监听到数据分类的变化需要提前调用一次方法；
        const datasetCategoryId = localStorage.getItem('datasetCategoryID');
        bindEvent(datasetCategoryId, filterType);

        event.on(`common:JNSelectSetter.bindEvent`, (value) => bindEvent(value, filterType));

        return () => {
            event.off(`common:JNSelectSetter.bindEvent`, (value) => bindEvent(value, filterType));
        }
    }, [])

    return (
        <Select
            autoWidth={false}
            size="small"
            value={value}
            dataSource={options}
            mode={mode}
            onChange={(val) => {
                onChange && onChange(val);
            }}
            style={{ width: '100%' }}
            showSearch={showSearch}
        />
    );
}

export default JNSelectSetter;