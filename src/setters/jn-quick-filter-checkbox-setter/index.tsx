import * as React from 'react';
import { Checkbox } from 'antd';
import { CheckboxValueType } from 'antd/es/checkbox/Group';
import _ from 'lodash';

interface CheckboxGroupSetterProps {
  value: string[];
  disabled: boolean;
  options: any;
  defaultValue: any;
  filters?: string[];
  onChange: (val: string) => void;
}

interface CheckboxGroupSetterState {
  setterValue: string | null;
}

const COMMON_TIME_FRAME = [
  {
    label: '日',
    value: 'day',
  },
  {
    label: '周',
    value: 'week',
  },
  {
    label: '月',
    value: 'month',
  },
  {
    label: '季度',
    value: 'quarter',
  },
  {
    label: '半年',
    value: 'half_year',
  },
  {
    label: '年',
    value: 'year',
  },
  {
    label: '自定义',
    value: 'custom',
  },
  {
    label: '全部',
    value: 'all',
  }
];

const CheckboxGroup = Checkbox.Group;

export default class JNQuickFilterCheckbox extends React.PureComponent<CheckboxGroupSetterProps> {
  static displayName = 'JNQuickFilterCheckbox';
  constructor(props: CheckboxGroupSetterProps) {
    super(props);
    console.log("props", props);
  }
  state: CheckboxGroupSetterState = {
    setterValue: null,
  };
 
  render() {
    const { onChange, value,filters, ...restProps } = this.props;
    
    console.log("props", filters);
    return (
      <div className="Checkboxgroup-style">
        <CheckboxGroup
          {...restProps}
          value={value}
          options={filters? _.filter(COMMON_TIME_FRAME, (item) => filters.includes(item.value)):  COMMON_TIME_FRAME }
          onChange={(val: CheckboxValueType[]) => {
            onChange(val as unknown as string);
          }}
        />
      </div>
    );
  }
}
