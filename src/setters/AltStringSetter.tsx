import * as React from "react";
import { Input } from "@alifd/next";

// import "./index.scss";
interface AltStringSetterProps {
  // 当前值
  value: string;
  // 默认值
  initialValue: string;
  // setter 唯一输出
  onChange: (val: string) => void;
  // AltStringSetter 特殊配置
  placeholder: string;
}
export default class AltStringSetter extends React.PureComponent<AltStringSetterProps> {
  componentDidMount() {
    const { onChange, value, defaultValue } = this.props;
    if (value == undefined && defaultValue) {
      onChange(defaultValue);
    }
  }

  // 声明 Setter 的 title
    static displayName = 'AltStringSetter';
  bindFunction = () => {
      const { field, value } = this.props;
      const propsField = field.parent;
          // 获取同级其他属性 showJump 的值
      const otherValue = propsField.getPropValue('showJump');
      // set 同级其他属性 showJump 的值
      propsField.setPropValue('showJump', false);
  }
  render() {
    const { onChange, value, placeholder } = this.props;
    return (
      <Input value={value}
        placeholder={placeholder || ""}
        onChange={(val: any) => onChange(val)}
      ></Input>
    );
  }
}