import * as React from 'react';
import { Input } from '@alifd/next';

interface JNStringSetterProps {
  value: string;
  defaultValue: string;
  placeholder: string;
  onChange: (val: string) => void;
}

export default class JNStringSetter extends React.PureComponent<JNStringSetterProps, any> {
  static displayName = 'StringSetter';

  render() {
    const { onChange, placeholder, value, ...restProps } = this.props;
    return (
      <Input
        size="small"
        value={value}
        placeholder={placeholder || ''}
        onChange={(val: any) => onChange(val)}
        style={{ width: '100%' }}
        {...restProps}
      />
    );
  }
}