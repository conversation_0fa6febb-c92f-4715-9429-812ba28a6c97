import * as React from "react";
import { AddJump ,setConfig} from "src/material/linkConfig/components/AddJump";
import { Button, Dialog } from '@alifd/next';
import { IPublicModelNode } from "@alilc/lowcode-types";


interface LinkBoxSetterProps {
  // 当前值
  value: string;
  defaultValue: any
  selected: IPublicModelNode
  // 默认值
  initialValue: string;
  // setter 唯一输出
  onChange: (val: any) => void;
  // AltStringSetter 特殊配置
  placeholder: string;
  onOpen: () => void;
  onOk: () => void;
  onCancel: () => void;
  
}
export default class LinkBoxSetter extends React.PureComponent<LinkBoxSetterProps> {
  onOk: (e: any) => void;
  onOpen: (e: any) => void;
  onClose: (triggerType:any, e: any) => void; 

  public state: {
    visible: boolean;
  };
  constructor(props:any) {

    super(props);
    this.state = {
      visible: false,
    };
    this.onOpen = () => {
      this.setState({
        visible: true,
      });
    };
    this.onOk = e => {
      console.log(e);
      const tempConfig = setConfig();
      this.props.onChange(tempConfig);
      this.setState({
        visible: false,
      });
    };
    this.onClose = (triggerType, e) => {
      console.log(triggerType, e);
      this.setState({
        visible: false,
      });
    };
  }
  // componentDidMount() {
  //   const { onChange, value, defaultValue } = this.props;
  //   if (value == undefined && defaultValue) {
  //     onChange(defaultValue);
  //   }
  // }

  // 声明 Setter 的 title
  static displayName = 'LinkBoxSetter';
  render() {
    const { selected, value } = this.props;
    const { visible } = this.state;
    return (
     <div >
        <Button onClick={this.onOpen} type="primary" size="small">
          跳转配置
        </Button>
        <Dialog
          v2
          title="创建跳转"
          visible={this.state.visible}
          wrapperClassName="linkDialog"
          onOk={this.onOk}
          onClose={this.onClose}
          onCancel={this.onClose}
        >
         <AddJump node={selected} isSetter={true} defaultValue={value}/>
        </Dialog>
      </div>
    );
  }
}