import React, { useState } from 'react';
import './index.less';
import { AuthInput } from './AuthInput';
import { MESSAGE } from './constants';
import { GetCodeAction } from './GetCodeAction';

interface MessageModalProps {
  handleMsgValidate: (value: boolean) => void;
}

export const MessageModal: React.FC<MessageModalProps> = (props) => {
  const { handleMsgValidate } = props;

  const [bindValidateId, setValidateId] = useState<number | undefined>();
  const [codes, setCodes] = useState<string[]>([]);

  const [validateErr, setValidateErr] = useState(false);
  const [validateErrMsg, setValidateErrMsg] = useState('');

  const handleCodesSet = (codes: string[]) => {
    setCodes(codes);
  };

  const handleValidateId = (id: number) => {
    setValidateId(id);
  };

  const handleValidateErr = (value: boolean) => {
    setValidateErr(value);
  };

  const handleValidateErrMsg = (value: string) => {
    setValidateErrMsg(value);
  };

  return (
    <div className="message-modal-container">
      <div className="message-modal">
        <img
          className="logo"
          src="https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/images/message_validate_logo.png"
        />
        <p className="title">{MESSAGE.Title}</p>
        <p className="sub-title">{MESSAGE.SubTitle}</p>
        <GetCodeAction
          handleCodesSet={handleCodesSet}
          handleValidateId={handleValidateId}
          handleValidateErr={handleValidateErr}
          handleValidateErrMsg={handleValidateErrMsg}
        />
        <AuthInput
          codes={codes}
          handleCodesSet={handleCodesSet}
          validateErr={validateErr}
          validateErrMsg={validateErrMsg}
          bindValidateId={bindValidateId}
          handleValidateErr={handleValidateErr}
          handleValidateErrMsg={handleValidateErrMsg}
          handleMsgValidate={handleMsgValidate}
        />
      </div>
    </div>
  );
};
