.message-modal-container {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;

  border-radius: 16px;
  background-color: #ffffff;
  background-size: cover;
  background-image: url(https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/images/message_validate_bg.png);

  .message-modal {
    min-width: 228px;
    min-height: 212px;
    border-radius: 16px;
    margin-top: -128px;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .logo {
      width: 80px;
      height: 80px;
      margin-block-end: 16px;
    }

    .title {
      color: #1d2129;
      font-size: 14px;
      font-family: font-fz-bold;
    }

    .sub-title {
      color: #4e5969;
      font-size: 12px;
      font-family: font-fz;
      margin-block-end: 32px;
    }

    .validate-action-row {
      width: 100%;

      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .action-label {
        color: #86909c;
        font-size: 12px;
      }

      .action-curr {
        color: #489cff;
        font-size: 12px;

        &:hover {
          cursor: pointer;
          opacity: 0.85;
        }
      }

      .action-count {
        color: #489cff;
        font-size: 12px;
      }
    }
  }
}

.auth-container {
  width: 100%;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .auth-title-list {
    width: 100%;

    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .auth-input {
      width: 36px;
      height: 36px;
      margin: 10px;

      font-size: 16px;
      line-height: 36px;
      text-align: center;

      border-radius: 8px;
      border: 1px solid #dde0ea;
    }

    .auth-input:first-child {
      margin-inline-start: 0;
    }

    .auth-input:last-child {
      margin-inline-end: 0;
    }

    margin-block-end: 4px;
  }

  .auth-validate-error {
    width: 100%;
    margin-block-end: 20px;

    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;

    .error-icon {
      font-size: 16px;
      margin-inline-end: 6px;
    }

    .error-msg {
      color: #f65b5b;
      font-size: 12px;
      font-family: font-fz;
    }
  }

  .auth-btn {
    width: 100% !important;
    height: 36px !important;
    font-size: 14px !important;
    border-radius: 8px !important;

    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }

  .auth-btn:disabled {
    color: #ffffff !important;
    background: #cdd0dc !important;
  }
}
