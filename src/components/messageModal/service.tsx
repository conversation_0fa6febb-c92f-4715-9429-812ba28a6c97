import axios from 'axios';

interface SMSResponse {
  code: string;
  msg: string;
  result: {
    expirationSeconds: number;
    id: number;
  } | null;
}

export const getSmsCode: () => Promise<SMSResponse> = async () => {
  return await axios.post('/common-app-api/sms/send-verification-code', {
    businessType: 'bjx.page.view',
  });
};

export const verifySmsCode: (config: {
  id: number;
  verifyCode: string;
}) => Promise<SMSResponse> = async (config) => {
  return await axios.post('/common-app-api/sms/verify-code', {
    id: config?.id,
    verificationCode: config?.verifyCode,
  });
};
