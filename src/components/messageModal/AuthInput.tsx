import { Button, message } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { MESSAGE } from './constants';
import { verifySmsCode } from './service';
import IconFont from 'src/setters/icon-setter/IconFont';

interface AuthInputProps {
  codes: string[];
  bindValidateId: number | undefined;
  validateErr: boolean;
  validateErrMsg: string;
  handleCodesSet: (codes: string[]) => void;
  handleValidateErr: (value: boolean) => void;
  handleValidateErrMsg: (value: string) => void;
  handleMsgValidate: (value: boolean) => void;
}

export const AuthInput = (props: AuthInputProps) => {
  const {
    codes,
    bindValidateId,
    validateErr,
    validateErrMsg,
    handleCodesSet,
    handleValidateErr,
    handleValidateErrMsg,
    handleMsgValidate,
  } = props;

  const [btnDisabled, setBtnDisabled] = useState(true);
  const inputsRef = useRef<HTMLInputElement[]>([]);

  const onEnterClick = () => {
    handleMsgValidate(false);
  };

  const handleInputChange = useCallback((index, event) => {
    const currentValue = event.target.value.match(/[0-9]{1}/) ? event.target.value : '';

    // 如果输入有效值, 则自动聚焦到下一个输入框
    if (currentValue) {
      inputsRef.current[index + 1]?.focus();
    }

    handleCodesSet((pre) => {
      const newData = [...pre];
      newData[index] = currentValue;
      return newData;
    });
  }, []);

  const handleInputForKey = useCallback(
    (index, event) => {
      const { key } = event;

      // 除删除键外的其它键值，直接跳出
      if (key !== 'Backspace') {
        return;
      }

      // 若当前输入框有值, 则删除当前输入框内容
      if (codes[index]) {
        handleCodesSet((pre) => {
          const newData = [...pre];
          newData[index] = '';
          return newData;
        });
      } else if (index > 0) {
        // 2. 如果当前输入框没有值(考虑下边界的情况 index === 0): 则删除上一个输入框内容, 并且光标聚焦到上一个输入框
        handleCodesSet((pre) => {
          const newData = [...pre];
          newData[index - 1] = '';
          return newData;
        });
        inputsRef.current[index - 1].focus();
      }
    },
    [codes],
  );

  const handlePaste = useCallback((event) => {
    const pastedValue = event.clipboardData.getData('Text'); // 读取剪切板数据
    const pastNum = pastedValue.replace(/[^0-9]/g, ''); // 去除数据中非数字部分, 只保留数字

    // 重新生成 codes: 4 位, 每一位取剪切板对应位置的数字, 没有则置空
    const newData = Array.from({ length: 4 }, (_, index) => pastNum.charAt(index) || '');

    handleCodesSet(newData); // 修改状态 codes

    // 光标要聚焦的输入框的索引, 这里取 pastNum.length 和 5 的最小值即可, 当索引为 5 就表示最后一个输入框
    const focusIndex = Math.min(pastNum.length, 5);
    inputsRef.current[focusIndex]?.focus();
  }, []);

  const verifySMS = async () => {
    if (bindValidateId !== undefined && codes.join('').length === 4) {
      const verifyRes = await verifySmsCode({ id: bindValidateId, verifyCode: codes.join('') });
      // console.log('[VerifyRes]', verifyRes);

      if (verifyRes?.result !== null) {
        setBtnDisabled(false);
        handleValidateErr(false);
        handleValidateErrMsg('');
      } else if (verifyRes?.result === null) {
        handleValidateErr(true);
        handleValidateErrMsg(verifyRes?.msg);
      }
    }
  };

  useEffect(() => {
    const initArr = Array.from({ length: 4 }, () => '');
    handleCodesSet(initArr);
  }, []);

  useEffect(() => {
    verifySMS();
  }, [codes, bindValidateId]);

  return (
    <div className="auth-container">
      <div className="auth-title-list">
        {codes.map((value, index) => {
          return (
            <input
              type="text"
              key={index}
              value={value}
              maxLength={1}
              className="auth-input"
              onPaste={handlePaste}
              onKeyDown={handleInputForKey.bind(null, index)}
              onChange={handleInputChange.bind(null, index)}
              ref={(ele: HTMLInputElement) => (inputsRef.current[index] = ele)}
            />
          );
        })}
      </div>
      <div
        className="auth-validate-error"
        style={{ visibility: validateErr ? 'visible' : 'hidden', height: validateErr ? 20 : 0 }}
      >
        <IconFont type="icon-a-icon-tishi-mian1x" className="error-icon" />
        <span className="error-msg">{validateErrMsg}</span>
      </div>
      <Button
        type="primary"
        disabled={btnDisabled}
        shape="round"
        size="large"
        onClick={onEnterClick}
        className="auth-btn"
      >
        {MESSAGE.EnterText}
      </Button>
    </div>
  );
};
