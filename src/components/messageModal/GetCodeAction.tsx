import React, { useEffect, useState } from 'react';
import { message } from 'antd';
import { ACT_MESSAGE } from './constants';
import { getSmsCode } from './service';

interface GetCodeActionProps {
  handleCodesSet: (codes: string[]) => void;
  handleValidateId: (id: number) => void;
  handleValidateErr: (value: boolean) => void;
  handleValidateErrMsg: (value: string) => void;
}

export const GetCodeAction = (props: GetCodeActionProps) => {
  const { handleCodesSet, handleValidateId, handleValidateErr, handleValidateErrMsg } = props;

  const [currAction, setCurrAction] = useState(ACT_MESSAGE.GetValidateLabel);
  const [countdown, setCountdown] = useState(0);

  const startCountdown = (seconds?: number) => {
    setCountdown(seconds || 60); // 设置倒计时默认值为60秒
  };

  const onSend = async () => {
    try {
      if (countdown === 0) {
        handleValidateErr(false);
        handleValidateErrMsg('');

        const initArr = Array.from({ length: 4 }, () => '');
        handleCodesSet(initArr);

        const smsRes = await getSmsCode();
        // console.log('[SMSRes]', smsRes);
        if (smsRes?.result && smsRes?.result?.id) {
          handleValidateId(smsRes?.result?.id);
          startCountdown(smsRes?.result?.expirationSeconds);
        } else if (!smsRes?.result && smsRes?.msg) {
          message.error(smsRes?.msg);
        }
      }
    } catch (error) {
      console.error('Error sending SMS code:', error);
    }
  };

  useEffect(() => {
    if (countdown > 0) {
      const intervalId = setInterval(() => {
        if (countdown === 1) {
          setCurrAction(ACT_MESSAGE.ReSend);
        }
        setCountdown((prevCountdown) => prevCountdown - 1);
      }, 1000);

      // 清除定时器，防止内存泄漏
      return () => clearInterval(intervalId);
    }
  }, [countdown]);

  return (
    <div className="validate-action-row">
      <span className="action-label">{ACT_MESSAGE.ValidateLabel}</span>
      <span className={countdown > 0 ? 'action-count' : 'action-curr'} onClick={onSend}>
        {countdown > 0 ? countdown : currAction}
      </span>
    </div>
  );
};
