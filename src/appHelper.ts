import axios from 'axios';

// 使用自定义封装的 Axios 而非官方 request，以方便直接注入请求头
export function createAxiosFetchHandler(config?: Record<string, unknown>) {
  return async function (options) {
    const requestConfig = {
      ...options,
      url: options.uri,
      method: options.method,
      data: options.params,
      headers: options.headers,
      ...config,
    };
    const response = await axios(requestConfig);
    return response;
  };
}

const appHelper = {
  requestHandlersMap: {
    fetch: createAxiosFetchHandler(),
  },
  utils: {
    demoUtil: (...params: any[]) => {
      console.log(`this is a demoUtil with params ${params}`);
    },
  },
  constants: {
    ConstantA: 'ConstantA',
    ConstantB: 'ConstantB',
  },
};
export default appHelper;
