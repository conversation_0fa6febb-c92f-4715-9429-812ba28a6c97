body {
  font-family: PingFang<PERSON>-<PERSON>, Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC-Light, Microsoft YaHei;
  font-size: 12px !important;
  background-color: transparent;

  * {
    box-sizing: border-box;
  }
}

body,
#lce-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  overflow: hidden;
  text-rendering: optimizeLegibility;
  -webkit-user-drag: none;
  -webkit-text-size-adjust: none;
  -webkit-touch-callout: none;
  -webkit-font-smoothing: antialiased;

  #engine {
    width: 100%;
    height: 100%;
  }
}

html {
  min-width: 1024px;
}

.save-sample {
  width: 80px;
  height: 30px;
  background-color: #5584FF;
  border: none;
  outline: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
}

.load-assets {
  width: 100px;
  height: 30px;
  background-color: #5584FF;
  border: none;
  outline: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
}

/*-------------------蕉内全局配置---------------------------------------------*/
.lce-page {
  padding: 20px;
  background-color: transparent;
}

// 全局引入字体
@font-face {
  font-family: font-fz;
  src: url('/src/assets/font/font-fz.TTF');
}

@font-face {
  font-family: font-fz-bold;
  src: url('/src/assets/font/font-fz-bold.TTF');
}

@font-face {
  font-family: number-mm;
  src: url('/src/assets/font/MiSans-Medium.ttf');
}

@font-face {
  font-family: number-mr;
  src: url('/src/assets/font/MiSans-Regular.ttf');
}

.edit-module{
  &:hover{
    outline: 1px solid #5584FF !important;
  }
}

.next-drawer{
  overflow: visible !important;
  width: 400px !important;
}


.lowcode-dialog-error-wrapper
{
  .next-dialog{
    width: 400px;

  }

  .next-dialog-body{
    color:red
  }
}
