.liandong {
    width: 800px;
    min-height: 400px;
    padding-top: 20;
    display: flex;
    >div{
        flex:1;
    }
    .left{
        max-width: 40%;
        height: 400px;
        overflow: hidden;
        .liandong-left-list{
            height: 350px;
            width: 100%;
            overflow-y: auto;
            margin-top: 10px;
            .next-list-item{
                cursor: pointer;
                padding: 10px 10px;
            }
        }
        .liandong-left-list-txt{
            width: 290px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
        .search-input{
            width: 100%;
        }
        .active{
            background-color: #cecece;
        }
    }
    .right{
        padding: 10px;
        .header{
            margin-bottom: 10px;
            .owner{ 
                display: inline-block;
                min-width: 90px;
                text-align: right;

            }
            .other{
                margin-left: 100px;
            }
        }
        .list{
            height: 350px;
            overflow-y: auto;
        }
        .list-item{
            margin-bottom: 10px;
            display: flex;
        }
        
        .label{
            margin-right: 10px;
            line-height: 32px;
            min-width:90px;
            text-align: right;

        }
        .select-box{
            flex:1;
            line-height: 32px;
            text-align: right;
        }
       
    }
}
 
.liandongwarp  {
    .next-dialog-body{
       max-height: 400px;
    }

}