import '../styles.less';
import { project } from '@alilc/lowcode-engine';
import { useMemo, useState, useEffect } from 'react';
import { Input, Icon, List, Checkbox, Balloon } from '@alifd/next';
import _ from 'lodash';
import { KeyWordsMap } from './KeywordsMap';
import {
  getComponentsByGroup,
  getMapDataById,
  setMapData,
  getComponentId,
  getDatasetList,
  getPropsDataComponentId,
} from '../../../services/liandong';
import React from 'react';
const Tooltip = Balloon.Tooltip;

interface ComponentListProps {
  componentName: string;
  id: string;
}

interface datasetListVO {
  id: string;
  datasetName: string;
}

export const SelectPanel = (props: { node: any }) => {
  const { node } = props;
  const { propsData } = node;
  const schema = project.exportSchema();

  const [dataSource, setDataSource] = useState<ComponentListProps[]>([]);
  const [searchValue, setSearchValue] = useState<string>();
  const [selected, setSelected] = useState<string[]>(
    getMapDataById(getPropsDataComponentId(propsData)),
  );
  const [current, setCurrent] = useState<ComponentListProps>();
  const [datasetList, setDatasetList] = useState<{ [key: string]: string }>({});

  // 获取数据集的名称

  const dataSourceList = useMemo<ComponentListProps[]>(() => {
    // 获取对应的组件  table 和 表格联动表格、表格联动趋势图。
    // const dataSourceDefault = (_.get(schema, 'componentsTree.[0].children') || [])
    let backList = getComponentsByGroup([
      'ComparedTable',
      'JNTableOfInter',
      'JNLineChart',
      'JNBarChart',
      'JNMixChart',
      'JNDoughnutCard',
      'JNSplitBarChart',
      'JNSplitBarChart_V2',
      "JNHeatMap",
      "JNDynamicChart",
      "JNMap",
      "JNMapGD"
    ]);
    if (backList.length > 0) {
      backList = backList.filter((item: any) => item?.id !== node?._id);
    }

    if (backList.length && !current) {
      setCurrent(backList[0]);
    }
    setDataSource([...backList]);
    return backList;
  }, []);
  // 获取对应的组件

  const handleSelect = (value: boolean, item: any) => {
    let newSelected;
    const componentId = getComponentId(item);
    const linkageId = dataSource.map((item) => getComponentId(item));

    if (value && componentId) {
      newSelected = [..._.filter(selected, (id) => linkageId.includes(id)), componentId];
      setSelected(newSelected);
    } else {
      newSelected = _.filter(selected, (selectedId) => selectedId !== componentId);
      // 刪除清空对应的选中数据
      setSelected(newSelected as string[]);
    }

    // 获取
    setMapData(getPropsDataComponentId(propsData), newSelected);
  };
  useEffect(() => {
    getDatasetListFn();
  }, []);
  const getDatasetListFn = async () => {
    const result = await getDatasetList();
    if (result?.list && result?.list?.length) {
      const datasetList: { [key: string]: string } = {};
      // Array.array.forEach(element => {

      // });
      result.list.forEach((item: { id: string; datasetName: string }) => {
        datasetList[item.id] = item.datasetName;
      });
      setDatasetList(datasetList);
    }
  };
  const handlerSearch = () => {
    const filterList = _.filter(dataSourceList, (dataSource) => {
      const id = getComponentId(dataSource) + '';
      const name = datasetList[`${id}`];
      if (name.indexOf(searchValue || '') > -1) {
        return true;
      } else if (id.indexOf(searchValue || '') > -1) {
        return true;
      } else {
        return false;
      }
    });
    setDataSource([...filterList]);
  };

  return (
    <div className="liandong">
      <div className="left">
        <Input
          style={{ width: '100%' }}
          innerAfter={
            <Icon type="search" size="xs" onClick={() => handlerSearch()} style={{ margin: 4 }} />
          }
          value={searchValue}
          onChange={(v) => setSearchValue(v)}
          placeholder="search"
          aria-label="input with config of innerAfter"
          onPressEnter={() => handlerSearch()}
        />
        <List
          size="small"
          className="liandong-left-list"
          header={<div>组件列表</div>}
          dataSource={[...dataSource]}
          renderItem={(item, i) => (
            <List.Item
              className={current?.id === item?.id ? 'active' : ''}
              onClick={() => setCurrent(item)}
              key={i}
            >
              <div className="liandong-left-list-txt">
                <Checkbox
                  defaultChecked={selected?.includes?.(getComponentId(item))}
                  onChange={(value) => handleSelect(value, item)}
                />
                &nbsp;
                <Tooltip
                  v2
                  trigger={`${datasetList[getComponentId(item)] || ''}[${getComponentId(item)}]`}
                >
                  {datasetList[getComponentId(item)]}[{getComponentId(item)}]
                </Tooltip>
              </div>
            </List.Item>
          )}
        />
      </div>
      <div className="right">
        <div className="header">
          <span className="owner">当前卡片使用字段</span>
          <span className="other">目标卡片关联字段</span>
        </div>
        <KeyWordsMap current={current} node={node} />
      </div>
    </div>
  );
};
