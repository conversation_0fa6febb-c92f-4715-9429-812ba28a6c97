/**
 * @param
 * 1. 做好组件之间的映射关系， 获取对应的数据维度
 * 2. 数据设计为
 * {
 *  filterMap: {
 *      [id]: {
 *          fildId:,
 *          name:
 *      }
 *  }
 * }
 * 
 */
import { useState, useEffect } from "react";
import { Select } from '@alifd/next';
import { getComponentId, getGlobalMapData, getPolarisListById, getPropsDataComponentId, setGlobalMapData } from "src/services/liandong";
import _ from "lodash";

interface KeyWordsMapProps {
    node: any,
    current: any
}
interface FieldMeta{
    dsId?: string
    fdId?: string;
    fdType?: string;
    metaType?: string
    name?: string;
}

interface KeyWord {
    id: string | number,
    key: string, //  默认key
    ref: string, // 树状在有这个
    title: string,
    name: string
    fieldMeta?: FieldMeta
}

const Option = Select.Option;

export const KeyWordsMap = (props:KeyWordsMapProps) => {

    const [isShow, setIsShow] = useState(true)
    const [dims, setDims] = useState<KeyWord[]>([])
    const [responseComponents, setResponseComponents] = useState<FieldMeta[]>([])
    const [defaultData, setDefaultData] = useState(getGlobalMapData())


    // 回显数据
    // const defaultData = getGlobalMapData();

    const { node, current } = props
    const { propsData, schema} = node
    const sId = getPropsDataComponentId(propsData)

    // 获取对应的数据源信息
    useEffect(() => {
        if(sId) {
           
            getSourceCompoentData(sId)
        }
    },[sId])
    // 获取联动
    const getSourceCompoentData = async(id: string) => {
        const result = await getPolarisListById(id)
        //  如果有值则找到对应的dim字段
        const resultDim = _.get(result, 'data.dim')
        // 处理dim
        if(resultDim) {
            const dim = resultDim.filter((item:{ title?:string, ref?: string}) => !!item.title && (_.get(item, 'fieldMeta.metaType') ==='DIM') ).map((item:any) => ({name:_.get(item, 'fieldMeta.title'), ...item}))
            const resultDims = []
            let currFdId = ''

            for (const item of dim) {
                if (item?.fieldMeta?.fdId !== currFdId) {
                    currFdId = item?.fieldMeta?.fdId
                    resultDims.push(item)
                }
            }


            setDims(resultDims)
        }

    }
    // 当前被联动的数据
    useEffect(() => {
        const currrentId =getComponentId(current)
        if(currrentId) {
            getResponseCompoentData(currrentId)
        }
    },[current?.props])
    // 获取被联动数据
    const getResponseCompoentData = async(id: string) => {
        setIsShow(false)
        const result = await getPolarisListById(id)
        //  如果有值则找到对应的dim字段
        const resultDim = _.get(result, 'data.dsFields')
        // 处理dim
        if(resultDim) {
            const dim = resultDim.filter((item:{ name?:string}) => !!item.name).map((item:any) => ({name:_.get(item, 'fieldMeta.name'), ...item}))

            setResponseComponents(dim)
            setDefaultData(getGlobalMapData())
            setIsShow(true)
        }

    }
    
    const handlerSelect = (keyWord:KeyWord, value:any) => {
       
        const selectItem = dims.find((item:KeyWord) => keyWord?.key === item.key)

        // 通过value 获取映射字段的完整信息
        // 当前选中的值
        const mapCurrent = responseComponents.find((item) => item?.fdId === value)
        // 获取桑倩的组件id
        const componentId = getComponentId(current)
        // 获取子映射字段的Id, 通过这个去匹配
        const keywordFdId = _.get(mapCurrent, 'fieldMeta.fdId')
        setGlobalMapData(`${sId}_${componentId}`, `${keyWord?.ref || keyWord!.key}`, {
            key: value,
            title: mapCurrent?.name || '',
            // node,
            // mapNode: current,
            // sourceNode: propsData,
            ...mapCurrent
        })

    }
    // console.log('[联动组件-Dims]', dims, isShow, defaultData)

    return <div className="list">
              
            {
                isShow && dims.map(m =><div className="list-item">
                    <label className="label">
                        {m.name}
                    </label>
                    <div className="select-box">
                        <span>
                           字段: &nbsp;
                        </span>
                        <Select showSearch={true} defaultValue={_.get(defaultData, `${sId}_${getComponentId(current)}.${m?.ref}.fdId`) || _.get(defaultData, `${sId}_${getComponentId(current)}.${m?.key}.fdId`) ||
                        _.get(defaultData, `${sId}_${getComponentId(current)}.${m?.key}.mapWordNode.fieldMeta.fdId`)  } 
                            onChange={(value) => handlerSelect(m, value)} style={{ marginRight: 8, minWidth: '60%' }}>
                            <Option value="">请选择</Option>
                            {responseComponents.map((item) =><Option value={item.fdId}>{item.name}</Option>)}
                 
                        </Select>
                    </div>

                </div>)
            }

        </div>
}