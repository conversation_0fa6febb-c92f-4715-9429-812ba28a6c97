import React from 'react';
import { material } from '@alilc/lowcode-engine';
import { Message, Dialog } from '@alifd/next';
import { SelectPanel } from './components/SelectPanel';
import { GlobalMapData, getGlobalMapData, getMapData, updatePageLinkageConfigById } from 'src/services/liandong';
import IconFont from 'src/setters/icon-setter/IconFont';
import { IPublicModelNode } from '@alilc/lowcode-types';

// 显示联动图标的组件白名单
const whiteList = [
  // 'JNCommonContainer',
  // 'JNObjectCard',s
  'JNTableOfInter',
  "ComparedTable",
  "JNDoughnutCard",
  "JNBar<PERSON>hart",
  "JNLineChart",
  "JNMixChart",
  "JNSplitBarChart",
  "JNSplitBarChart_V2",
  "JNHeatMap",
  "JNDynamicChart",
  "JNMap",
]
material.addBuiltinComponentAction({
  name: '联动',
  content: {
    icon: <IconFont style={{ fontSize: '16px' }} type='icon-a-icon-liandong1'/>,
    title: '联动',
    action(node: IPublicModelNode) {
      let mapData = {};
      const setMapData = (data: any) => {
        mapData = data
      }
      Dialog.confirm({
        v2: true,
        title: "联动组件",
        wrapperClassName: "liandongwarp",
        footer: null,
        content: <SelectPanel node={node} />,
        onOk: () => {
          const globalData = getGlobalMapData()
          const saveGlobalData:GlobalMapData = {}
          const mapData = getMapData()
          // 对比数据是否能够匹配   处理数据，将老数据，或者没有选择的数据进行清除掉
          Object.keys(globalData).forEach(key => {
            const [parentId, id] = key.split('_')

            if (mapData[parentId] && mapData[parentId].includes(+id)) {
              saveGlobalData[key] = globalData[key]
            }
          })


          // 更新页面的联动数据
          return updatePageLinkageConfigById({ mapData, globalData: saveGlobalData, linkConfig: window?.pageConfig?.linkConfig }).then((res:any) => {
            Message.success(res?.msg || "保存成功!");
          });
        }
      });
    },  
  },
  important: true,
  condition: (node: IPublicModelNode) => {
    return whiteList.includes(node?.componentName)
  }
});