import { material } from '@alilc/lowcode-engine';
import { Icon, Message, Dialog } from '@alifd/next';
import IconFont from 'src/setters/icon-setter/IconFont';
import { AddJump, setConfig } from './components/AddJump';
import { getGlobalMapData, getMapData, updatePageLinkageConfigById } from 'src/services/liandong';
import React from 'react';

// 显示创建跳转图标的组件白名单
const whiteList = [
    "JNBarChart",
    "JNLineChart",
    "JNMixChart",
    "JNSplitBarChart",
    "ComparedTable",
    "DynamicColumnTable",
    "JNTableOfInter",
    'JNDoughnutCard',
    'JNRetailList',
    'JNCategoryRankList',
    "JNSplitBarChart_V2",
]


material.addBuiltinComponentAction({
    name: '跳转',
    content: {
        icon: <IconFont style={{ fontSize: '16px' }} type='icon-icon-biaotoupaixumoren' />,
        title: '跳转',
        action(node) {
            Dialog.confirm({
                v2: true,
                title: "创建跳转",
                wrapperClassName: "linkDialog",
                footer: null,
                content: <AddJump node={node} />,
                onOk: () => {
                    const globalData = getGlobalMapData()
                    const saveGlobalData = {}
                    const mapData = getMapData()
                    // 对比数据是否能够匹配   处理数据，将老数据，或者没有选择的数据进行清除掉
                    Object.keys(globalData).forEach(key => {
                        const [parentId, id] = key.split('_')

                        if (mapData[parentId] && mapData[parentId].includes(+id)) {
                            saveGlobalData[key] = globalData[key]
                        }
                    })

                    let linkConfig = {};
                    if (window.pageConfig) {
                        const componentId = node.id;
                        const tempConfig = setConfig();
                        const allLinkConfig = window.pageConfig.linkConfig || {};
                        linkConfig = { ...allLinkConfig };
                        linkConfig[componentId] = tempConfig;

                        window.pageConfig.linkConfig = linkConfig;
                    }

                    console.log('linkConfig===================', linkConfig);

                    // 更新页面的联动数据
                    return updatePageLinkageConfigById({ mapData, globalData: saveGlobalData, linkConfig }).then((res) => {
                        Message.success(res?.msg || "保存成功!");
                    });
                },
            })
        }
    },
    condition: (node: Node) => {
        return whiteList.includes(node?.componentName)
    },
    important: true,
});