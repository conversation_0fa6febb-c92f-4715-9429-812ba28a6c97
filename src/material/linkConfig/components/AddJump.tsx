import React, { useState, useEffect } from "react";
import {  Radio, Checkbox } from 'antd';
import { Input } from '@alifd/next';
import { DefaultParamMap, FilterParamMap } from './ParamMap';
import '../style.less';
import { Select ,Switch} from '@alifd/next';
export let setConfig: () => { jumpto: string; targetPage: null; openType: string; paramState: boolean; paramList: never[]; filterParamState: boolean; polarisUrl?: string; filterParamList: never[]; };

export const AddJump = (props:any) => {
    const { node, isSetter = false, defaultValue } = props;
    // console.log('node----------', node);
    const [jumpto, setJumpto] = useState('instrumentPanel');
    const [targetPage, setTargetPage] = useState('');
    const [polarisUrl, setPolarisUrl] = useState('');
    const [openType, setOpneType] = useState('newTab');
    const [withPath, setWithPath] = useState(false);
    const [paramState, setParamState] = useState(false);
    const [filterParamState, setFilterParamState] = useState(false);
    const [hasAnchorPoint, setHasAnchorPoint] = useState(false);
    const [anchorPoint, setAnchorPoint] = useState();
    const [paramList, setParamList] = useState([]);
    const [filterParamList, setFilterParamList] = useState([]);
    const [pageList, setPageList] = useState([]);
    const selectDataSource = window.dict?.pageList || [];
    setConfig = () => {
        return {
            jumpto,
            targetPage,
            polarisUrl,
            openType,
            paramState,
            paramList,
            filterParamState,
            filterParamList,
            withPath,
            hasAnchorPoint,
            anchorPoint
        }
    }

    useEffect(() => {
        // 初始化下拉页面信息
        if (selectDataSource?.list) {
            let tempList = selectDataSource?.list?.map(item => {
                return {
                    label: `${item?.pageName || ''}[${item?.id}]`,
                    value: item?.id
                }
            });

            const param = new URLSearchParams(window.location.search);
            const id = param.get('id');

            tempList = tempList.filter(item => `${item.value}` !== id);
            // console.log('tempList', tempList);
            setPageList(tempList);
        }
        if(isSetter) {
            const config = defaultValue || {}
            setJumpto(config?.jumpto || 'instrumentPanel');
            setTargetPage(config?.targetPage || '');
            setOpneType(config?.openType || 'newTab');
            setParamState(config?.paramState || false);
            setParamList(config?.paramList || []);
            setFilterParamState(config?.filterParamState || false);
            setFilterParamList(config?.filterParamList || []); 
            setPolarisUrl(config?.polarisUrl ||''); 
            setWithPath(config?.withPath || false);
            setHasAnchorPoint(config?.hasAnchorPoint || false);
            setAnchorPoint(config?.anchorPoint || '');
        }else{
            const config = window?.pageConfig?.linkConfig?.[node.id] || {};
            setJumpto(config?.jumpto || 'instrumentPanel');
            setTargetPage(config?.targetPage || '');
            setOpneType(config?.openType || 'newTab');
            setParamState(config?.paramState || false);
            setParamList(config?.paramList || []);
            setFilterParamState(config?.filterParamState || false);
            setFilterParamList(config?.filterParamList || []); 
            setPolarisUrl(config?.polarisUrl ||''); 
            setWithPath(config?.withPath || false);
            setHasAnchorPoint(config?.hasAnchorPoint || false);
            setAnchorPoint(config?.anchorPoint || '');
        }
        
    }, []);

    const jumpChange = (value) => {
        if (jumpto && value !== jumpto) {
            setTargetPage('');
            setOpneType('newTab');
            setParamState(false);
            setParamList([]);
            setFilterParamState(false);
            setFilterParamList([]);
        }
        setJumpto(value);
    }

    const targetPageChange = (value) => {
        
        // console.log('@@@@', value);
        if(jumpto === 'instrumentPanel' && targetPage !== value){
            let temp1 = [...paramList];
            temp1?.forEach(item => {
                item.targetPageFilter = '';
            });
            setParamList(temp1);

            let temp2 = [...filterParamList];
            temp2?.forEach(item => {
                item.targetPageFilter = '';
            });
            setFilterParamList(temp2);
        }
        setTargetPage(value)
    }

    return <div className="jn-link-dialog">
        <div className="row-item">
            <span>跳转至：</span>
            <Radio.Group onChange={(e) => { jumpChange(e.target.value) }} value={jumpto}>
                <Radio value={'instrumentPanel'}>仪表板</Radio>
                <Radio value={'link'}>链接</Radio>
            </Radio.Group>
        </div>
        {
            <div className="row-item">
                <span>目标页面：</span>
                {jumpto === 'instrumentPanel' ?
                    <Select value={targetPage}
                        style={{ width: 240 }}
                        placeholder=''
                        showSearch={true}
                        popupContainer={() => document.querySelector('.jn-link-dialog')}
                        onChange={(value) => { targetPageChange(value) }}
                        // allowClear={true}
                        dataSource={pageList}
                        // optionFilterProp='label'
                    />:
                    <Input value={targetPage} onChange={(val) => setTargetPage(val)} />
                }
            </div>
        }
        {
           jumpto === 'instrumentPanel' ? <div className="row-item">
            <span>北四页面url: </span>
                <Input hasClear value={polarisUrl} onChange={(val) => setPolarisUrl(val as string)} />
            </div>: null
        }
       <div className="row-item">
            <span>带路径: </span>
                <Switch checked={withPath} size="small" onChange={(checked:boolean) => setWithPath(checked)} />
        </div>
        
        <div className="row-item">
            <span>打开方式：</span>
            <Radio.Group onChange={(e) => { setOpneType(e.target.value) }} value={openType}>
                <Radio value={'pullOutPop'}>抽拉式弹窗</Radio>
                <Radio value={'middlePop'}>中间式弹窗</Radio>
                <Radio value={'newTab'}>新页签</Radio>
            </Radio.Group>
        </div>
        {
            <div className="row-item"> <Checkbox onChange={() => { setParamState(!paramState) }} checked={paramState}>带过滤条件跳转</Checkbox></div>
        }
        {
            paramState ? <DefaultParamMap paramList={paramList} filterParamList={filterParamList} setParamList={setParamList} node={node} targetPageId={targetPage} jumpto={jumpto} /> : null
        }
        {
            <div className="row-item"><Checkbox onChange={() => { setFilterParamState(!filterParamState) }} checked={filterParamState}>带筛选器条件跳转</Checkbox> </div>
        }
        {
            filterParamState ? <FilterParamMap filterParamList={filterParamList} paramList={paramList} setFilterParamList={setFilterParamList} node={node} targetPageId={targetPage} jumpto={jumpto} /> : null
        }
        <div className="row-item"> <Checkbox onChange={() => { setHasAnchorPoint(!hasAnchorPoint) }} checked={hasAnchorPoint}>跳转至电梯导航锚点</Checkbox></div>
        {
            hasAnchorPoint ? <div className="row-item">
                <span>组件ID：</span>
                <Input placeholder="请输入已设置锚点的组件ID" value={anchorPoint} onChange={(val) => setAnchorPoint(val)} />
            </div> : null
        }


    </div>
}
