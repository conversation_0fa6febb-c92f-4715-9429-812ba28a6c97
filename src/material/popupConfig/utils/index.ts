import _ from 'lodash';
import axios from 'axios';

export const getFilterMap = (array: any, idList: any[],) => {
    // const projectSchema = project.exportSchema(IPublicEnumTransformStage.Save)
    // const componentsTree:any = _.get(projectSchema, 'componentsTree[0].children')
    const filterName = ['JNCommonFilterBar', 'JNFilterBar', 'JNSubFilterBar', 'JNAreaTabs', 'JNDisplayFilter', 'JNQuickFilter'];
    array?.forEach((item: any) => {

        if (filterName.includes(item.componentName)) {
            // console.log('item===========', item);
            if (item?.props?.filters) {
                idList.push(...item?.props?.filters.map((filter: { filterId: string, label: string }) => {
                    return { label: `${filter.label || ''}[${filter.filterId}]`, value: filter.filterId + '', filter }
                }))
                // 通用组件外面一个是时间filterId 里面还有filters结构， 做下兼容
                if (item.componentName === 'JNCommonFilterBar' && item?.props?.filterId) {
                    idList.push({ label: `${item.props.label || ''}[${item.props.filterId}]`, value: item.props.filterId + '', filter:item.props })
                }
            } else if (item?.props?.filterId) {
                idList.push({ label: `${item.props.label || ''}[${item.props.filterId}]`, value: item.props.filterId + '' , filter: item.props})
            }
        }

        if (item.children) {
            getFilterMap(item.children, idList)
        }

        // 针对插槽内的组件
        const contentChildren = _.get(item, 'props.content.value')
        if (contentChildren && contentChildren?.length) {
            getFilterMap(contentChildren, idList)
        }
    })
}

export const getPageList = async () => {
    const data = await axios.get(`/bjx-app-api/page/pageListSimplified?page=1&size=9999`);
    return new Promise(resolve => {
        if (data?.code === 'ok') {
            resolve(data?.result)
        } else {
            console.error(data)
        }
    })
}