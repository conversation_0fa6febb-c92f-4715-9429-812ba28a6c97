import React, { useEffect, useState } from "react";
import { project } from '@alilc/lowcode-engine';
import { IPublicEnumTransformStage } from '@alilc/lowcode-types';
import { DeleteOutlined, } from '@ant-design/icons';
import { Select } from '@alifd/next';
import { Input } from '@alifd/next';
import IconFont from "src/setters/icon-setter/IconFont";
import { getFilterMap } from '../utils/index';
import { getPolarisListById, } from "src/services/liandong";
import axios from 'axios'
import _, { set } from 'lodash';
import { getPageInfo } from "src/utils/pageInfo";

const getSourceComponentData = async ({ id, setFun, componentName }: { id: string, setFun: any, componentName?: string }) => {
    let dim = [];
    let result = {};

    if (componentName && componentName === 'DynamicColumnTable') {
        const data = await axios.post(`/bjx-app-api/dataset/dynamicData?id=${id}`, {
            id,
            limit: 10,
            offset: 0,
            filters: [],
        });
        if (data?.code === 'ok') {
            result = data?.result
        } else {
            console.error(data)
        }
    } else {
        result = await getPolarisListById(id);
    }
    //  如果有值则找到对应的dim字段
    const resultDim = _.get(result, 'data.dim');
    // 处理dim
    if (resultDim) {
        dim = resultDim.filter((item: { title?: string }) => !!item.title && _.get(item, 'fieldMeta.metaType') === 'DIM').map((item: any) => ({ label: _.get(item, 'fieldMeta.title'), value: item.ref, ...item }));
    }
    setFun(dim);
}

const pageDetail = async (id: string, setFun: any) => {
    let filterList: { label: string; value: any; filter:any }[] = [];
    const response = await axios.get(`/bjx-app-api/page/detail?id=${id}`);
    if (response?.code === 'ok') {
        const pageSchema = JSON.parse(response?.result?.projectSchema);
        const componentsTree: any = _.get(pageSchema, 'componentsTree[0].children');
        const {biFilters} = getPageInfo(componentsTree, id)
        biFilters?.map(item => {
            filterList.push({
                label: `${item?.label}[${item.id}]`,
                value: item.id,
                filter: item
            })
        })
      
    
    }

    setFun(filterList);
    return filterList;
}
const getPageDetail = async (id: string) => {
    let filterList: { label: string; value: any; }[] = [];
    const response = await axios.get(`/bjx-app-api/page/detail?id=${id}`);
    if (response?.code === 'ok') {
        const pageSchema = JSON.parse(response?.result?.projectSchema);
        const componentsTree: any = _.get(pageSchema, 'componentsTree[0].children');

        return componentsTree;
    }

   
    return [];
}
export const DefaultParamMap = (props) => {
    const { paramList, filterParamList, setParamList, node, targetPageId, jumpto } = props;
    const [options, setOptions] = useState([]);
    const [filterList, setFilterList] = useState([]);
    const [filterData, setFilterData] = useState([]);

    const addParamItem = () => {
        let tempData = [...paramList, { dataKey: '', targetPageFilter: '' }];
        setParamList(tempData);
    }

    const deleteParamItem = (i) => {
        let tempData = [...paramList];
        tempData.splice(i, 1);
        setParamList(tempData);
    }

    const paramChange = (i, key, value) => {
        let tempData = [...paramList];
        tempData[i][key] = value;
        if(key === 'targetPageFilter'){
            const filter = _.find(filterList, (item) => item?.filter.id === value);
            if(filter){
                tempData[i].filter = filter?.filter || [];

            }
        }
        setParamList(tempData);
    }

    useEffect(() => {
        getSourceComponentData({ id: node.propsData?.specificDataset?.bindDataset || node.propsData?.id, setFun: setOptions, componentName: node?.schema?.componentName });
    }, []);

    useEffect(() => {
        if (targetPageId) {
            jumpto === 'instrumentPanel' ? pageDetail(targetPageId, setFilterList).then(res => setFilterData(res)) : null;
        } else {
            setFilterList([]);
            let temp = [...paramList];
            temp?.forEach(item => {
                item.targetPageFilter = '';
            });
            setParamList(temp);
        }
    }, [targetPageId]);

    return <div className="row-item">
        <div>
            <span className="tag">当前卡片使用字段</span><span className="tag">目标页面筛选器</span>
        </div>
        {
            paramList?.map((item, i) => {
                return <div className="param-item">
                    <Select style={{ width: 150 }}
                        value={item.dataKey}
                        onChange={(value) => { paramChange(i, 'dataKey', value) }}
                        placeholder='选择数据字段'
                        showSearch={true}
                        dataSource={options}
                        popupContainer={() => document.querySelector('.jn-link-dialog')}
                    />
                    {
                        jumpto === 'instrumentPanel' ?
                            <Select style={{ width: 150 }}
                                value={item.targetPageFilter}
                                onChange={(value) => { paramChange(i, 'targetPageFilter', value) }}
                                placeholder='选择目标筛选器'
                                showSearch={true}
                                dataSource={filterList}
                                popupContainer={() => document.querySelector('.jn-link-dialog')}
        
                                optionFilterProp='label'
                                allowClear={true}
                                onFocus={() => {
                                    const tempList = [...paramList, ...filterParamList];
                                    let newList: any[] = []
                                    filterData?.map(item => {
                                        const _has = tempList?.some(_item => _item?.targetPageFilter === item.value)
                                        if (!_has) {
                                            newList.push(item);
                                        }
                                    })

                                    setFilterList(newList);
                                }}
                            /> :
                            <Input style={{ width: '150px', height: '30px' }} value={item.targetPageFilter} onChange={(val) => paramChange(i, 'targetPageFilter', val)} />
                    }

                    <DeleteOutlined style={{ fontSize: '16px' }} onClick={() => deleteParamItem(i)} />
                </div>
            })
        }
        <span className="add-item" onClick={addParamItem}>
            <IconFont type="icon-icon-xinzeng" />
            添加
        </span>
    </div>
}

export const FilterParamMap = (props:any) => {
    const { paramList, filterParamList, setFilterParamList, node, targetPageId, jumpto } = props;
    const [filterList, setFilterList] = useState([]);
    const [filterData, setFilterData] = useState([]);

    const projectSchema = project.exportSchema(IPublicEnumTransformStage.Save);
    const componentsTree: any = _.get(projectSchema, 'componentsTree[0].children');
    const options: any[] = [];
    getFilterMap(componentsTree, options);

    const addFilterParamItem = () => {
        let tempData = [...filterParamList, { currentPageFilter: '', targetPageFilter: '' }];
        setFilterParamList(tempData);
    }

    const deleteFilterParamItem = (i) => {
        let tempData = [...filterParamList];
        tempData.splice(i, 1);
        setFilterParamList(tempData);
    }

    const filterParamChange = (i, key, value) => {
        let tempData = [...filterParamList];
        tempData[i][key] = value;
        if(key === 'targetPageFilter'){
            const filter = _.find(filterList, (item) => item?.filter.id === value);
            if(filter){
                tempData[i].filter = filter?.filter || [];

            }
        }
        setFilterParamList(tempData);
    }

    useEffect(() => {
        pageDetail(targetPageId, setFilterList);
        if (targetPageId) {
            jumpto === 'instrumentPanel' ? pageDetail(targetPageId, setFilterList).then(res => setFilterData(res)) : null;
        } else {
            setFilterList([]);
            let temp = [...filterParamList];
            temp?.forEach(item => {
                item.targetPageFilter = '';
            });
            setFilterParamList(temp);
        }
    }, [targetPageId]);

    return <div className="row-item">
        <div>
            <span className="tag">当前页面筛选器</span><span className="tag">目标页面筛选器</span>
        </div>
        {
            filterParamList.map((item, i) => {
                return <div className="param-item">
                    <Select style={{ width: 150 }}
                        allowClear={true}
                        value={item.currentPageFilter}
                        onChange={(value) => { filterParamChange(i, 'currentPageFilter', value) }}
                        placeholder='选择页面当前筛选器'
                        showSearch={true}
                        dataSource={options}
                        popupContainer={() => document.querySelector('.jn-link-dialog')}

                        optionFilterProp='label'
                    />

                    {
                        jumpto === 'instrumentPanel' ?
                            <Select style={{ width: 150 }}
                                allowClear={true}
                                value={item.targetPageFilter}
                                onChange={(value) => { filterParamChange(i, 'targetPageFilter', value) }}
                                placeholder='选择目标页面筛选器'
                                showSearch={true}
                                dataSource={filterList}
                                popupContainer={() => document.querySelector('.jn-link-dialog')}
        
                                optionFilterProp='label'
                                onFocus={() => {
                                    const tempList = [...paramList, ...filterParamList];
                                    let newList: any[] = []
                                    filterData?.map(item => {
                                        const _has = tempList?.some(_item => _item?.targetPageFilter === item.value)
                                        if (!_has) {
                                            newList.push(item);
                                        }
                                    })

                                    setFilterList(newList);
                                }}
                            /> :
                            <Input style={{ width: '150px', height: '30px' }} value={item.targetPageFilter} onChange={(val) => filterParamChange(i, 'targetPageFilter', val)} />
                    }

                    <DeleteOutlined style={{ fontSize: '16px' }} onClick={() => deleteFilterParamItem(i)} />
                </div>
            })
        }
        <span className="add-item" onClick={addFilterParamItem}>
            <IconFont type="icon-icon-xinzeng" />
            添加
        </span>
    </div>
}


// 卡片选择
export const CardParamMap = (props:any) => {
    const {  card, setCard, node, targetPageId, jumpto } = props;
    const [componentsTree, setComponentsTree] = useState([]);
    const [ options, setOptions] = useState([]);

  
    


    // 获取选中页面的卡片信息
    /**
     * "componentStyle": {
              "groupTitle": "业绩整体概览",


     */
    useEffect(() => {
        getPageDetail(targetPageId).then(componentsTree => {
           const result =  _.chain(componentsTree)
           
            .filter((item) =>  !['JNSubFilterBar', 'JNQuickFilter', 'JNPageAnchor'].includes(item?.componentName))
            .map((item) => {
                const title = _.get(item, 'props.title') || _.get(item, 'props.componentStyle.groupTitle') ; 
                return {
                    label: title ? `[${title}]${item?.id}`: item?.id ,
                    value: item?.id 
                }
            }).value();
            setOptions(result);
            setComponentsTree(componentsTree);

        });

    },[targetPageId] );

    const handleCardChange = (value: string ) => {
        if(value){
            const detail = _.find(componentsTree, { id: value });
            setCard({id: value, value:detail, type:"JSSlot"})
          
        }
    }

    return <Select style={{ width: 150 }}
                        allowClear={true}
                        value={card?.id || ''}
                        onChange={(value) => handleCardChange(value as string) }
                        placeholder='选择页面卡片'
                        showSearch={true}
                        dataSource={options}
                        popupContainer={() => document.querySelector('.jn-link-dialog')}

                        optionFilterProp='label'
                    />
 
}