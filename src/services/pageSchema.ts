import _ from 'lodash';
import { ResponseData } from './typescript';
import { PageRO } from './page';
import axios from 'axios';
// 处理page  schema 
/**
 * 获取组件信息
 */
let componentsMap:any = null;
const getComponentInfo = async(item: any) => {
  // 组件映射表格
  // console.log('item', item)
  // 判断组件是否有columns
  if (item?.props?.columns) {
    // console.log('columns', item?.props?.columns)
    await getColumns(item?.props?.columns)
  }
  


}

// 递归处理columns含有 children的子类， 并且处理item中hasPopup 为true的数据
const getColumns = async (columns: any) => {
  for(let k = 0; k < columns.length; k++) {
    const item = columns[k];
    if (item.hasPopup) {
      // console.log('item', item)
      const popupConfigList = item?.popupConfigList || [];
      for(let i = 0; i < popupConfigList.length; i++) {
        const pagePopupConfig = popupConfigList[i];
        const nodeId = _.get(pagePopupConfig, 'popupConfig.card.id') 
        const pageId = _.get(pagePopupConfig, 'popupConfig.targetPage')
        if(pageId && nodeId) {
          const response: ResponseData<PageRO> = await axios.get(`/bjx-app-api/page/detail?id=${pageId}`);
          if(response.code === 'ok'){
            const {projectSchema} = response.result;
            const targetPageSchema = JSON.parse(projectSchema)
            const componentsTree: any = _.get(targetPageSchema, 'componentsTree[0].children');
            const currentPageComponentsMap = _.get(targetPageSchema, 'componentsMap');
            const detail =  _.find(componentsTree, { id: nodeId });
            if(detail) {
              pagePopupConfig.popupConfig.card.value = [detail];
              if(componentsMap){
                const union = currentPageComponentsMap.reduce((acc:any, item:any) => {
                  if (!_.find(acc, { componentName: item.componentName })) {

                    acc.push(item);
                  }else{
                    // console.log('item', item)
                  }
                  return acc;
                }, componentsMap)
                componentsMap = union;
              }
            }else{
              pagePopupConfig.popupConfig.card = null;
            }
            // console.log('projectSchema', projectSchema) 
          }else{
            pagePopupConfig.popupConfig.card = null;
          }
        }

      }
      // 获取弹框数据

    }
    if (item.children) {
      getColumns(item.children)
    }
  }
}


export const getPageData = async(pageComponentsTree: any) => {

  for(let i = 0; i < pageComponentsTree.length; i++) {
      const item = pageComponentsTree[i];
      const contentChildren = _.get(item, 'props.content.value')
      if (contentChildren && contentChildren?.length) {
          await getPageData(contentChildren)
      }
      // 处理数据形式
      await  getComponentInfo(item)


      if (item.children) {
        await getPageData(item.children)
      }

      //针对表格自定义插槽
      if (item.props?.tableContent?.value) {
        await   getPageData(item.props?.tableContent?.value);
      }
      if (item?.content?.type === 'JSSlot' && item?.content?.value) {
          // console.log(item)
          await  getPageData(item?.content?.value);
      }
      // 针对指标容器
      if (item?.childrenContent?.value) {
        await   getPageData(item.childrenContent.value);
      }
   
      // 针对下钻容器内
      if (item.componentName === 'JNDrillingContainer' &&item.props?.dataList ) {
        await  getPageData(item.props?.dataList);
      }
      // 针对下钻容器内
      if (item.componentName === 'JnIndicatorGroupBox' && item.props?.slotList) {
        await  getPageData(item.props?.slotList);
      }
      // 针对标签页容器
      if (item.componentName === 'JNTabsContainer' && item.props?.tabs) {
          
          await getPageData(item.props?.tabs,);
      }

  }
}


export const handlePageSchema = async (pageSchema:any) => {
  // 获取页面数据， 并修改有弹框的组件数据
  // console.log('pageSchema', pageSchema)
  componentsMap = [...pageSchema.componentsMap];
  await getPageData(pageSchema.componentsTree)
  // console.log('componentsMap', componentsMap)
  pageSchema.componentsMap = componentsMap;
  // return pageSchema;
}