/*
 * @Author: kiki
 * @Date: 2023-09-13 18:38:46
 * @LastEditTime: 2023-11-06 11:42:58
 * @LastEditors: kiki
 * @Description:
 */
/**
 * 默认数据 mapdata
 *
 * 获取当前页面的mapData字典
 * 保存的时候的mapData
 * 1. 组件数据对应两个部分， 一个选中关联的组件， 一个是组件的默认值
 *
 *  selectedArray // 哪些组件被关联了
 *
 *
 *  guanlianMap // 组件映射关系每个字段映射哪一个
 *  {
 *      [id]: {
 *         key1: {
 *          label:'',
 *          key:mapKey1,
 *         },
 *         key2: {
 *          label:'',
 *          key:mapKey2,
 *         },
 *         key3: {
 *          label:'',
 *          key: mapKey2,
 *         }
 *
 *      }
 *  }
 *
 *
 */

import axios from 'axios';
import { project } from '@alilc/lowcode-engine';
import { IPublicTypeProjectSchema, IPublicEnumTransformStage } from '@alilc/lowcode-types';
import _ from 'lodash';
import { PageLinkageConfigRO } from './page';

export interface GlobalMapData {
  [key: string]: {
    [key: string]: mapInfo;
  };
}

export interface mapInfo {
  key: string;
  node?: any;
  title: string;
  mapNode?: any;
  mapWordNode?: any;
  sourceNode?: any; // node的propsData
}

export interface MapDataInfo {
  // name: string; // 字段的名称
  // key: string;  // 字段的key
  [key: string]: number[];
}

export interface ResponseBody<T>{
  code:string;
  result:T;
  msg?:string;
}

let mapData: MapDataInfo = {};
let globalMapData: GlobalMapData = {};
let pageLinkageConfig: PageLinkageConfigRO = {};

// 初始化页面的mapDat 和globalMapData
export const init = (map: MapDataInfo, globalMap: GlobalMapData, info: PageLinkageConfigRO) => {
  mapData = map || {};
  globalMapData = globalMap || {};
  pageLinkageConfig = info || {};
};
// 设置组件的  mapData
export const setMapData = (id: string, map: any) => {
  if (!map?.length) {
    delete mapData[id];
  } else {
    mapData[id] = map;
  }
};
// 获取当前组件的管理信息
export const getMapDataById = (id: string) => {
  return mapData[id] || [];
};
export const getMapData = () => {
  return mapData || {};
};

// 设置组件的全局的
export const setGlobalMapData = (id: string, keyword: string, data: mapInfo) => {
  // 检查是不是已经勾选了

  if (globalMapData[id]) {
    globalMapData[id][keyword] = data;
  } else {
    globalMapData[id] = {
      [keyword]: data,
    };
  }
};

// // 设置组件的  mapData
// export const setGlobalMapData = (data:GlobalMapData) => {
//     globalMapData = data
// };

// 设置组件的  mapData
export const getGlobalMapData = () => {
  return globalMapData || {};
};

// 根据数据id获取对应的字段
export const getPolarisListById = async (id: string) => {
  const data:ResponseBody<any> = await axios.post('/bjx-app-api/dataset/data', {
    id,
    limit: 10,
    offset: 0,
    filters: [],
  });
  if (data?.code === 'ok') {
    return data?.result;
  } else {
    console.error(data);
  }
};

// 根据数据id 获取维度字段
export const getDimensionalityById = (id: string) => {};

export const getFilterMap = (array: any, idList: string[]) => {
  array.forEach((item: any) => {
    if (item.componentName === 'JNFilterBar' || item.componentName === 'JNSubFilterBar') {
      if (item?.props?.filters) {
        idList.push(
          ...item?.props?.filters.map((filter: { filterId: string }) => filter.filterId + ''),
        );
      } else if (item?.props?.filterId) {
        idList.push(item.props.filterId + '');
      }
    }
    if (item.children) {
      getFilterMap(item.children, idList);
    }

    // 针对插槽内的组件
    const contentChildren = _.get(item, 'props.content.value');
    if (contentChildren && contentChildren?.length) {
      getFilterMap(contentChildren, idList);
    }
  });
};

// 根据对应的出入组件类型， 筛选出对应的所有组件列表
const getComponentsByGroupFn = (array: any, group: string[], idList: any[]) => {
  array.forEach((item: any) => {
    if (group.includes(item.componentName)) {
      if (item?.props?.id || item?.props?.bindDataset || item?.props?.specificDataset?.bindDataset) {
        idList.push(item);
      }
    }
    if (item.children) {
      getComponentsByGroupFn(item.children, group, idList);
    }

    // 针对插槽内的组件
    const contentChildren = _.get(item, 'props.content.value');
    if (contentChildren && contentChildren?.length) {
      getComponentsByGroupFn(contentChildren, group, idList);
    }

    // 针对插槽内的组件
    const dataList = _.get(item, 'props.dataList');
    if (dataList && dataList?.length) {
      getComponentsByGroupFn(dataList, group, idList);
    }
    const dataListContent = _.get(item, 'content.value');
    if (dataListContent && dataListContent?.length) {
      getComponentsByGroupFn(dataListContent, group, idList);
    }
  });
};

export const getComponentsByGroup = (group: string[]) => {
  const backData: any = [];
  const projectSchema = project.exportSchema(IPublicEnumTransformStage.Save);
  const componentsTree = _.get(projectSchema, 'componentsTree[0].children');
  // 获取对应的页面
  getComponentsByGroupFn(componentsTree, group, backData);

  return backData;
};

// 根据数据 更新页面的联动数据
export const updatePageLinkageConfigById = async (content: any) => {
  console.log('updatePageLinkageConfigById');
  if (!pageLinkageConfig.id || !pageLinkageConfig?.pageId) {
    console.error('数据信息问题!');
    return;
  }
  const data:ResponseBody<any> = await axios.post('/bjx-app-api/pageLinkageConfig/update', {
    id: pageLinkageConfig?.id,
    pageId: pageLinkageConfig?.pageId,
    content: JSON.stringify(content),
  });
  return new Promise((resolve) => {
    if (data?.code === 'ok') {
      resolve(data?.result);
    } else {
      console.error(data);
    }
  });
};

// 获取数据集列表
export const getDatasetList = async () => {
  const data:{code: string, result: { list: any}} = await axios.get('/bjx-app-api/dataset/pageList?size=100000&page=1');
  return new Promise((resolve) => {
    if (data?.code === 'ok') {
      resolve(data?.result);
    } else {
      console.error(data);
    }
  });
};
export const getComponentId = (item?: any): string => {
  return (
    item?.props?.id ||
    item?.props?.bindDataset ||
    item?.props?.specificDataset?.bindDataset ||
    item?.specificDataset?.bindDataset ||
    ''
  );
};

// 从props里面找到数据的真实id
export const getPropsDataComponentId = (item?: any): string => {
  return item?.id || item?.bindDataset || item?.specificDataset?.bindDataset || '';
};

if (window.JNTools) {
  window.JNTools.getGlobalMapData = getGlobalMapData;
  window.JNTools.getMapData = getMapData;
} else {
  window.JNTools = {
    getGlobalMapData,
    getMapData,
  };
}
