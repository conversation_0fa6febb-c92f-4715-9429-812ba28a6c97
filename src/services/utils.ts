import axios from 'axios';
import _ from 'lodash';

// 获取URL上的参数
export function getPathParams(index: number): string {
  const pathArr = window.location.pathname.split('/');
  return pathArr[index + 1];
}

// 获取参数
export function getUrlSearchParams(key: string): string {
  if (location?.search && key) {
    const searchParams = new URLSearchParams(location.search);
    return searchParams.get(key) || '';
  }
  return '';
}

const getSortFilter = () => {
  const tempComponentsFilterMap = [...window?.pageConfig?.componentsFilterMap];
  const tempFilterEffectRanges = { ...window?.pageConfig?.filterEffectRanges };
  let sortFilter: string[] = [];
  let temp = tempComponentsFilterMap.reduce((obj, key) => {
    obj[key] = {
      count: 1, // 计算
      affectedFilterList: [], // 被哪些筛选器影响
    };
    return obj;
  }, {});

  const keysList = Object.keys(tempFilterEffectRanges);
  if (tempFilterEffectRanges && keysList.length) {
    for (const key in tempFilterEffectRanges) {
      tempFilterEffectRanges[key]?.forEach((item) => {
        if (temp && temp[item?.id] && item?.type === 'filter') {
          temp[item?.id].affectedFilterList.push(key);
          temp[item?.id].count += temp[key].count;
        } else {
          temp = {
            ...temp,
            [item?.id]: {
              count: 1 + temp[key]?.count,
              affectedFilterList: [key],
            },
          };
        }
      });
    }
  }

  sortFilter = [...tempComponentsFilterMap];
  for (let i = 0; i < sortFilter.length - 1; i++) {
    for (let j = 0; j < sortFilter.length - i - 1; j++) {
      if (temp?.[sortFilter[j]]?.count > temp?.[sortFilter[j + 1]]?.count) {
        [sortFilter[j], sortFilter[j + 1]] = [sortFilter[j + 1], sortFilter[j]];
      }
    }
  }

  return { sortFilter, filterEffectRanges: temp };
};

// 设置筛选器默认值
export const setFilterFirstDefaultValue = async () => {
  let filterValue = '';
  let tempValue: any[] = [];
  const sortFilters = getSortFilter();

  const getFilterValue = (obj, withPath) => {
    if (obj?.children && obj?.children?.length) {
      withPath && tempValue.push(obj?.key?.split('_')[0]);
      getFilterValue(obj?.children[0], withPath);
    } else {
      tempValue.push(obj?.key?.split('_')[0]);
    }
  };
  for (let i = 0; i < sortFilters?.sortFilter?.length; i++) {
    const filterItem = sortFilters?.sortFilter?.[i];
    // 判断url链接是否带参
    if (Object.keys(getUrlParams()).includes(filterItem)) {
      return;
    }

    let tempItem = window?.pageConfig?.defaultFilters?.[filterItem];
    // 设置下拉筛选器默认值为第一个
    if (tempItem?.defalutType === 'firstValue') {
      // 请求参数
      const tempFilterParam: {
        filterId: string;
        queryAllData: number;
        filters: any[];
        isUseSelfToken?: boolean;
      } = {
        filterId: tempItem?.id,
        queryAllData: 0,
        filters: [],
      };
      // 移除用户权限过滤逻辑
      // if(window.isUseSelfToken){
      //     tempFilterParam.isUseSelfToken = true;
      // }

      const affectedFilterList =
        sortFilters?.filterEffectRanges?.[filterItem]?.affectedFilterList || [];
      affectedFilterList?.forEach((_item: any) => {
        const filter = window.filters?.[_item] || window.pageConfig.defaultFilters[filterItem];
        filter?.filterValue && filter?.filterValue?.length && tempFilterParam.filters.push(filter);
      });

      // 如果带有数据id,默认找对应数据的第一个
      //当前筛选器

      if (tempItem?.dataId) {
        // 如果是选择了全部
        if (tempItem?.isShowAll) {
          // 不进行设置默认值
          continue;
        }
        // 请求数据
        const res = await axios.post(`/bjx-app-api/dataset/data?id=${tempItem?.dataId}`, {
          id: tempItem?.dataId,
          limit: 10,
          offset: 0,
          filters: _.filter(
            Object.values(window?.filters),
            (filter) => filter?.filterValue?.length,
          ),
          // filters:window?.filters,
          // 移除token相关参数
          // isUseSelfToken: tempFilterParam.isUseSelfToken,
        });
        if (res?.code === 'ok') {
          const column = res?.result?.data?.headList?.[0];
          const columnData = res?.result?.data?.bodyList?.[0];
          const filterValueList = [columnData?.[column?.key]];
          // window.pageConfig.defaultFilters[item] = { ...tempItem, filterValue: filterValue ? [filterValue] : [] };
          window.filters[filterItem] = { ...tempItem, filterValue: filterValueList };
        }
      } else {
        // 请求数据
        const res = await axios.post(
          `/bjx-app-api/filter/detail?filterId=${tempItem?.id}`,
          tempFilterParam,
        );
        if (res?.code === 'ok') {
          const cdType = res?.result?.filterMeta?.cdType;
          const withPath = res?.result?.withPath;
          const filterValueList = res?.result?.filterValue || res?.result?.treeFilterValue;
          if (cdType === 'TREE_SELECTOR') {
            tempValue = [];
            getFilterValue(filterValueList?.[0], withPath);
            filterValue = withPath ? tempValue.join('@_@') : tempValue.join(',');
          } else {
            filterValue = filterValueList?.[0]?.value;
          }

          // window.pageConfig.defaultFilters[item] = { ...tempItem, filterValue: filterValue ? [filterValue] : [] };
          window.filters[filterItem] = {
            ...tempItem,
            filterValue: filterValue ? [filterValue] : [],
            pathFilterRegex: res?.result?.withPath ? '@_@' : '',
          };
        }
      }
    }
  }
};

// 设置筛选器关联关系
export const setFiltersEffectRanges = (filter: { filterId: string; effectRanges: any[] }) => {
  if (window.pageConfig) {
    if (window.pageConfig.filterEffectRanges) {
      window.pageConfig.filterEffectRanges[filter.filterId] = filter.effectRanges;
    } else {
      window.pageConfig.filterEffectRanges = {
        [filter.filterId]: filter.effectRanges,
      };
    }
  } else {
    window.pageConfig = {
      filterEffectRanges: {
        [filter.filterId]: filter.effectRanges,
      },
    };
  }
};

// 获取存在的filter组件
export const getFilterMap = (array: any, idList: string[], partFilters: string[]) => {
  const filterName = [
    'JNCommonFilterBar',
    'JNFilterBar',
    'JNSubFilterBar',
    'JNAreaTabs',
    'JNDisplayFilter',
    'JNQuickFilter',
    'JNCombinationFilter',
    'JNPolarisFilterBar',
  ];
  array?.forEach((item: any) => {
    // 局部筛选器
    if (item.componentName === 'JNSubFilterBar') {
      if (item?.props?.filters) {
        const tempList = item?.props?.filters.map(
          (filter: { filterId: string; effectRanges: any[] }) => {
            setFiltersEffectRanges(filter);
            return filter.filterId + '';
          },
        );
        partFilters.push(...tempList);
      }
    }

    if (filterName.includes(item.componentName)) {
      if (item?.props?.filters) {
        const tempList = item?.props?.filters.map(
          (filter: { filterId: string; effectRanges: any[] }) => {
            setFiltersEffectRanges(filter);
            return filter.filterId + '';
          },
        );
        idList.push(...tempList);
        // 通用组件外面一个是时间filterId 里面还有filters结构， 做下兼容
        if (item.componentName === 'JNCommonFilterBar' && item?.props?.filterId) {
          setFiltersEffectRanges(item.props);
          idList.push(item.props.filterId + '');
        }
      } else if (item?.props?.filterId) {
        setFiltersEffectRanges(item.props);
        idList.push(item.props.filterId + '');
      }
    }

    if (item.children) {
      getFilterMap(item.children, idList, partFilters);
    }

    //针对表格自定义插槽
    if (item.props?.tableContent?.value) {
      getFilterMap(item.props?.tableContent?.value, idList, partFilters);
    }
    if (item?.content?.type === 'JSSlot') {
      getFilterMap(item?.content?.value, idList, partFilters);
    }

    // 针对下钻容器内
    if (item.componentName === 'JNDrillingContainer') {
      getFilterMap(item.props?.dataList, idList, partFilters);
    }

    // 针对插槽内的组件
    const contentChildren = _.get(item, 'props.content.value');
    if (contentChildren && contentChildren?.length) {
      getFilterMap(contentChildren, idList, partFilters);
    }
  });
};

// 获取指标列表数据
export const getIndicatorList = async () => {
  const response = await axios.post('/index-app-api/indicator/queryPage', {
    page: 1,
    size: 9999,
    approveStatus: 20,
  });
  if (response.code === 'ok') {
    const list = response.result.list;
    const options = list.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });

    window.jnIndicatorList = options;
  }
};

// 获取页面应用的指标列表详细信息
export const getIndicatorListDetail = async (ids: string[]) => {
  if (!ids.length) return;
  const response = await axios.post('/index-app-api/indicator/get-details-batch', {
    idList: ids,
  });
  if (response.code === 'ok') {
    const list = response?.result || [];
    window.jnIndicatorListDetail = list;

    // 兼容上个版本使用 jnIndicatorManagementLevel
    const obj = {};
    list.forEach((item) => {
      obj[item.id] = item;
    });
    window.jnIndicatorManagementLevel = obj;
  }
};

// 获取url参数
export const getUrlParams = () => {
  const isIframe = getUrlSearchParams('sc') === 'iframe';
  let url = isIframe ? window.parent.location.href : window.location.href;

  // 处理#node_oclrafqg6s1 锚点信息, 如果发现这个锚点信息, 则正则replace掉
  if (url.includes('#node')) {
    url = url.replace(/#node_[a-zA-Z0-9]{10,20}/, '');
  }
  console.log('url', url);
  let urlStr = url?.split('?')?.[1]?.split('&') || [];
  let newUrlStr = '';
  urlStr.map((item, i) => {
    const itemTemp = item.split('=');
    let itmeStr = '';

    itemTemp.map((_item, _i) => {
      if (_i === itemTemp.length - 1) {
        itmeStr += encodeURIComponent(_item);
      } else {
        itmeStr += `${encodeURIComponent(_item)}=`;
      }
    });

    if (i === urlStr.length - 1) {
      newUrlStr += itmeStr;
    } else {
      newUrlStr += `${itmeStr}&`;
    }
  });
  const urlSearchParams = new URLSearchParams(newUrlStr);
  const result = Object.fromEntries(urlSearchParams.entries());

  return result;
};

// 解析url特殊符号 <转化为? >转化为&
export const analysisSymbol = (value: any) => {
  let tempValue = value;
  tempValue = value.replaceAll('<', '?').replaceAll('>', '&');
  return tempValue;
};

export const setFilterValueByUrlParams = () => {
  const urlParams = getUrlParams(); // 页面参数
  const filterIds = window?.pageConfig?.componentsFilterMap; // 全部筛选器id集合

  filterIds &&
    filterIds?.map((item: string) => {
      const _has = decodeURI(_.get(urlParams, item));
      // 遍历筛选器判断参数是否存在
      if (![undefined, 'undefined', null, 'null', ''].includes(_has)) {
        const newValue = analysisSymbol(_has);
        // 在默认值或者时间筛选器配置中查找相关信息
        const defaultData = window?.pageConfig?.defaultFilters?.[item] ||
          window?.pageConfig?.timeFilter?.[item] || { filterType: 'IN', id: item };

        window.filters[item] = {
          ...defaultData,
          filterValue: newValue.split(','),
        };
      }
    });
};

import { handleDateTimeFilterDefaultValue, getDateRangeByKey } from 'src/utils/time';
export const setFilterValueByTimerFilter = (filterItem, quickFilterPage) => {
  let resultFilterValue =
    filterItem?.defalutType && filterItem?.defalutType === 'dynamicValue'
      ? handleDateTimeFilterDefaultValue(
          filterItem.filterValue,
          filterItem?.selectorType,
          filterItem?.filterType,
        )
      : getDateRangeByKey(filterItem.filterValue, quickFilterPage);

  // 判断url链接是否带参
  if (Object.keys(getUrlParams()).includes(filterItem?.id?.toString())) {
    const urlParams = getUrlParams(); // 页面参数
    const tempValue = decodeURI(_.get(urlParams, filterItem?.id));
    const newValue = analysisSymbol(tempValue);
    resultFilterValue = newValue.split(',');
  }

  // 处理范围时间筛选器联动筛选器默认值
  const componentsFilterMap = [...window.pageConfig?.componentsFilterMap];
  const filterEffectRanges = { ...window.pageConfig?.filterEffectRanges };
  const itemEffectRanges = filterEffectRanges[filterItem?.id] || filterItem?.effectRanges; //
  if (itemEffectRanges && itemEffectRanges?.length) {
    itemEffectRanges?.forEach((rangItem) => {
      if (rangItem?.type !== 'data' && componentsFilterMap.includes(rangItem?.id)) {
        switch (rangItem.value) {
          case 'start':
            window.filters[rangItem?.id] = {
              id: rangItem?.id,
              filterType: rangItem?.filterType,
              filterValue: resultFilterValue?.[0] ? [resultFilterValue?.[0]] : [],
            };

            break;

          case 'end':
            window.filters[rangItem?.id] = {
              id: rangItem?.id,
              filterType: rangItem?.filterType,
              filterValue: resultFilterValue[1] ? [resultFilterValue[1]] : [],
            };
            break;

          default:
            break;
        }
      }
    });
  }

  window.filters[filterItem.id] = {
    ...filterItem,
    filterValue: resultFilterValue,
  };
};
