/**
 * 字典数据，全部数据
 *
 *
 */

import axios from 'axios';
import { ResponseBody } from './liandong';
import { getPageList } from 'src/material/linkConfig/utils';

const dict: { [key: string]: any } = {};
window.dict = dict;
// 获取数据集的列表

export const getPageDataList = async () => {
  try {
    const pageDataList = await axios.get('/bjx-app-api/dataset/pageList', {
      params: {
        size: 10000,
        page: 1,
      },
    });
    // console.log('===getPageDataList====', pageDataList);
    if (pageDataList.code === 'ok') {
      dict.pageDataList = pageDataList.result;
      return pageDataList.result;
    } else {
      return [];
    }
  } catch (error) {
    console.error(error);
  }
};

/**
 * 获取所有的北极星筛选器
 */
export const getFilterTree = async () => {
  try {
    const filterTree: ResponseBody<any> = await axios.get('/bjx-app-api/gy/filterTree');
    if (filterTree.code === 'ok') {
      dict.filterTree = filterTree.result;
      return filterTree.result;
    } else {
      return [];
    }
  } catch (error) {
    console.error(error);
  }
};

/**
 *
 * init
 * 1. 获取快捷时间筛选器配置列表  dict.quickTimeFilterList 渲染和编辑页面需要
 * 2.  获取所有的页面列表  dict.pageList  编辑页面需要
 * 3. 获取所有数据集列表  dict.pageDataList  编辑页面需要
 * 4. 获取所有筛选器的分类  dict.filterTree  编辑页面需要
 */

export const dictInit = () => {
  const promiseList = [queryActionFilterPageList()]

  // 全局字典数据 
  if(!window.onClient){

    //  获取所有的页面列表
    getPageList().then((res) => {
      dict.pageList = res;
    });
    promiseList.push(getPageDataList());
    getFilterTree();
  }
  // 获取所有的分类
  return Promise.all(promiseList);
};

//获取分类数据
export const getPageDatasetCategoryList = async () => {
  try {
    const pageDataList = await axios.get('/bjx-app-api/datasetCategory/list', {
      params: {
        page: 1,
        size: 100000,
      },
    });
    if (pageDataList.code === 'ok') {
      return pageDataList.result;
    } else {
      return [];
    }
  } catch (error) {
    console.error(error);
  }
};

// 获取快捷时间筛选器配置列表
export const queryActionFilterPageList = async () => {
  try {
    const pageDataList = await axios.get('/bjx-app-api/dynamicParam/pageList', {
      params: {
        page: 1,
        size: 10000,
      },
    });
    // console.log('===queryActionFilterPageList====', pageDataList);
    if (pageDataList.code === 'ok') {
      dict.quickTimeFilterList = pageDataList?.result?.list;
      return pageDataList.result.list;
    } else {
      return [];
    }
  } catch (error) {
    console.error(pageDataList?.msg || '查询时间筛选器列表错误！');
  }
};

// 同步资源

// 获取快捷时间筛选器配置列表
export const refreshCache = async () => {
  try {
    const pageDataList = await axios.get('/bjx-app-api/gy/refreshCache', {});
    if (pageDataList.code === 'ok') {
      return pageDataList?.result;
    } else {
      return [];
    }
  } catch (error) {
    console.error(pageDataList?.msg || '查询时间筛选器列表错误！');
  }
};
