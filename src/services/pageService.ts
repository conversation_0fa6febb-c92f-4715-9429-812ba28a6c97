import { material, project } from '@alilc/lowcode-engine';
import { filterPackages } from '@alilc/lowcode-plugin-inject';
import { Message, Dialog } from '@alifd/next';
import { IPublicTypeProjectSchema, IPublicEnumTransformStage } from '@alilc/lowcode-types';
import DefaultPageSchema from './defaultPageSchema.json';
import DefaultI18nSchema from './defaultI18nSchema.json';
import axios from 'axios';
import {
  getUrlSearchParams,
  setFilterFirstDefaultValue,
  setFilterValueByUrlParams,
  setFilterValueByTimerFilter,
  getFilterMap,
  getIndicatorList,
  getIndicatorListDetail,
} from './utils';
import _ from 'lodash';
import { init } from './liandong';
import { setPageGlobalFontSize } from 'src/utils';
import { getPageInfo } from 'src/utils/pageInfo';
import { ResponseData } from './typescript';
import { PageLinkageConfigRO, PageRO, PageView } from './page';
import { handlePageSchema } from './pageSchema';

let globalApp: any = {};
let pageData: PageRO = {};

const generateProjectSchema = (pageSchema: any): IPublicTypeProjectSchema => {
  return {
    componentsTree: [pageSchema],
    componentsMap: material?.componentsMap as any,
    version: '1.1.0',
    // i18n: i18nSchema,
  };
};

export const serviceInit = async () => {
  try {
    const id = getUrlSearchParams('id');
    if (!id) {
      console.error('项目id未找到');
      return;
    }
    // 获取参数
    const response: ResponseData<PageRO> = await axios.get(`/bjx-app-api/page/detail?id=${id}`);
    try {
      const pageView: ResponseData<PageView> = await axios.get(
        `/bjx-app-api/pageView/viewInit?pageId=${id}`,
      );
      // console.log('[pageView]', pageView)
      // 非编辑页面才会有视图
      if (window.onClient && pageView?.result?.content) {
        window.pageView = JSON.parse(pageView.result.content);
        window.pageViewId = pageView?.result?.id;
      }
    } catch (error) {
      console.error(error, error);
    }

    const pageLinkageConfigResult: ResponseData<PageLinkageConfigRO> = await axios.get(
      `/bjx-app-api/pageLinkageConfig/findByPageId?pageId=${id}`,
    );

    // console.log('[Id-Res]', response)

    if (response.code === 'ok') {
      const {
        config,
        packages,
        projectSchema,
        pageState,
        pageType,
        pageName,
        validateUserToken,
        hasCipherKey,
        biIndicators,
      } = response.result;
      setPageGlobalFontSize(response?.result?.pageType === 'phone');

      // 移除token验证逻辑
      // if (validateUserToken && window.onClient) {
      //   window.isUseSelfToken = true
      // } else {
      //   window.isUseSelfToken = false
      // }

      //  设置是否加密请求体
      if (hasCipherKey && window.onClient) {
        window.hasCipherKey = true;
      } else {
        window.hasCipherKey = false;
      }
      // 设置页面是否使用当前用户权限
      if (pageName && window.onClient) {
        document.title = pageName;
      }

      if (biIndicators && biIndicators.length) {
        await getIndicatorListDetail(biIndicators.map((item) => item?.indicatorId || ''));
      }

      // 设置页面的全局配置
      globalApp.pageType = pageType;

      if (config) {
        window.pageConfig = JSON.parse(config);

        if (window?.pageConfig?.defaultFilters) {
          window.filters = _.cloneDeep(window?.pageConfig?.defaultFilters);
        } else {
          window.filters = {};
        }

        setFilterValueByUrlParams();

        // 时间范围的初始化是实时的， 需要单数对时间做逻辑, 针对动态时间有作用， 非动态时间没有反应
        if (window?.pageConfig?.timeFilter) {
          Object.values(window?.pageConfig?.timeFilter).forEach((item: any) => {
            setFilterValueByTimerFilter(item, window?.dict?.quickTimeFilterList || []);
          });
        }

        // 设置下拉筛选器默认值为第一个
        await setFilterFirstDefaultValue();
      }

      // 对schema数据进行处理， 找到所有的表格数据， 并找到映射的模块还原数据
      const buildProjectSchema = { ...JSON.parse(projectSchema || '{}') };
      await handlePageSchema(buildProjectSchema);

      pageData = {
        packages: JSON.parse(packages || '{}') || [],
        projectSchema: buildProjectSchema,
        pageState,
      };

      getIndicatorList();
    }
    // 初始化页面联动信息信息
    if (pageLinkageConfigResult?.code === 'ok' && pageLinkageConfigResult?.result) {
      const { mapData, globalData } = pageLinkageConfigResult.result.content
        ? JSON.parse(pageLinkageConfigResult.result.content)
        : { mapData: {}, globalData: {} };
      const mapInfo = JSON.parse(pageLinkageConfigResult?.result?.content || '{}');
      window.pageConfig = {
        ...window.pageConfig,
        linkConfig: mapInfo?.linkConfig || {},
      };
      init(mapData, globalData, pageLinkageConfigResult?.result);
    }

    if (response?.code === 'ok') {
      return response?.result;
    }
  } catch (error) {
    console.error(error, error);
  }
};

export const getPageState = (scenarioName: string) => {
  if (!scenarioName) {
    console.error('scenarioName is required!');
    return;
  }
  return pageData?.pageState;
};

export const saveSchema = async (scenarioName: string = 'unknown') => {
  setProjectSchemaToLocalStorage(scenarioName);
  await setPackagesToLocalStorage(scenarioName);
  const packages = await filterPackages(material.getAssets().packages);
  const projectSchema = project.exportSchema(IPublicEnumTransformStage.Save);
  // 检查组件库里面是否有config里面的组件， 没有就进行删除操作，不能入库
  const componentsTree = _.get(projectSchema, 'componentsTree[0].children');
  const componentsFilterMap: string[] = [];
  const partFilters: string[] = [];
  // 获取联动信息
  getFilterMap(componentsTree, componentsFilterMap, partFilters);

  // 页面信息统计信息
  const pageInfo = getPageInfo(componentsTree, getUrlSearchParams('id'));
  console.log('pageInfo', pageInfo);

  // 兼容老数据， 对比老数据的filterId 如果没有就直接删除
  if (window?.pageConfig) {
    window.pageConfig.componentsFilterMap = componentsFilterMap;
    window.pageConfig.partFilters = partFilters;
    if (
      window?.pageConfig?.filterId &&
      !componentsFilterMap.includes(window?.pageConfig?.filterId)
    ) {
      delete window.pageConfig.filterId;
    }
  }

  // 处理设置了默认值的数据
  if (window?.pageConfig && window?.pageConfig?.defaultFilters) {
    Object.keys(window.pageConfig.defaultFilters).forEach((item: string) => {
      if (!componentsFilterMap.includes(item)) {
        delete window.pageConfig.defaultFilters[item];
      }

      // 根据筛选器类型对filterValue进行处理
      const filterItem = window.pageConfig.defaultFilters[item];
      if (filterItem?.defalutType === 'firstValue') {
        window.pageConfig.defaultFilters[item] = {
          ...filterItem,
          filterValue: [],
        };
      }
    });
  }
  // 处理时间范围的数据
  if (window?.pageConfig && window?.pageConfig?.timeFilter) {
    Object.keys(window.pageConfig.timeFilter).forEach((item: string) => {
      if (!componentsFilterMap.includes(item)) {
        delete window.pageConfig.timeFilter[item];

        // 判断时间类型筛选器 默认值是否存在 不存在删除
      } else if (
        !window.pageConfig.timeFilter[item]?.filterValue ||
        !window.pageConfig.timeFilter[item]?.filterValue?.toString()?.length
      ) {
        delete window.pageConfig.timeFilter[item];
      }
    });
  }

  // 处理筛选器关联关系
  if (window?.pageConfig && window?.pageConfig?.filterEffectRanges) {
    Object.keys(window.pageConfig.filterEffectRanges).forEach((item: string) => {
      if (!componentsFilterMap.includes(item)) {
        delete window.pageConfig.filterEffectRanges[item];
      }
    });
  }

  const filterIds = componentsFilterMap.map((item) => item).toString();
  const data = {
    id: getUrlSearchParams('id'),
    config: JSON.stringify(window.pageConfig || {}),
    packages: JSON.stringify(packages),
    projectSchema: JSON.stringify(projectSchema),
    filterIds,
    ...pageInfo,
  };

  // TODO:
  // const updateSchema: ResponseData = await axios.post('/bjx-app-api/page/update', data);
  // if (updateSchema.code === 'ok') {
  //   Message.success(updateSchema.msg || '成功保存数据');
  // } else {
  //   Dialog.confirm({
  //     v2: true,
  //     title: '提示',
  //     content: updateSchema.msg || '保存失败了！',
  //     okProps: { children: '返回页面列表' },
  //     cancelProps: { children: '取消' },
  //     onOk: () => {
  //       window.location.href = '/polaris-admin/pages';
  //     },
  //   });
  // }
};

export const resetSchema = async (scenarioName: string = 'unknown') => {
  try {
    await new Promise<void>((resolve, reject) => {
      Dialog.confirm({
        content: '确定要重置吗？您所有的修改都将消失！',
        onOk: () => {
          resolve();
        },
        onCancel: () => {
          reject();
        },
      });
    });
  } catch (err) {
    return;
  }
  const defaultSchema = generateProjectSchema(DefaultPageSchema);
  project.importSchema(defaultSchema as any);
  project.simulatorHost?.rerender();

  setProjectSchemaToLocalStorage(scenarioName);
  await setPackagesToLocalStorage(scenarioName);
  Message.success('成功重置页面');
};

const getLSName = (scenarioName: string, ns: string = 'projectSchema') => `${scenarioName}:${ns}`;

export const getProjectSchemaFromLocalStorage = (scenarioName: string) => {
  if (!scenarioName) {
    console.error('scenarioName is required!');
    return;
  }

  // 优先从pageData获取数据（编辑器模式）
  if (pageData?.projectSchema) {
    return pageData.projectSchema;
  }

  // 如果pageData为空，从localStorage获取数据（预览模式）
  const localValue = window.localStorage.getItem(getLSName(scenarioName));
  if (localValue) {
    try {
      return JSON.parse(localValue);
    } catch (error) {
      console.error('Failed to parse projectSchema from localStorage:', error);
    }
  }

  return undefined;
};

// 保存数据
const setProjectSchemaToLocalStorage = (scenarioName: string) => {
  if (!scenarioName) {
    console.error('scenarioName is required!');
    return;
  }
  window.localStorage.setItem(
    getLSName(scenarioName),
    JSON.stringify(project.exportSchema(IPublicEnumTransformStage.Save)),
  );
};

// 页面数据，page
const setPackagesToLocalStorage = async (scenarioName: string) => {
  if (!scenarioName) {
    console.error('scenarioName is required!');
    return;
  }
  const packages = await filterPackages(material.getAssets().packages);
  window.localStorage.setItem(getLSName(scenarioName, 'packages'), JSON.stringify(packages));
};

// 获取依赖项目的数据
export const getPackagesFromLocalStorage = (scenarioName: string): string[] => {
  if (!scenarioName) {
    console.error('scenarioName is required!');
    return [];
  }

  // 优先从pageData获取数据（编辑器模式）
  if (pageData?.packages) {
    return pageData.packages;
  }

  // 如果pageData为空，从localStorage获取数据（预览模式）
  const localValue = window.localStorage.getItem(getLSName(scenarioName, 'packages'));
  if (localValue) {
    try {
      return JSON.parse(localValue);
    } catch (error) {
      console.error('Failed to parse packages from localStorage:', error);
    }
  }

  return [];
};

// 项目
export const getProjectSchema = async (
  scenarioName: string = 'unknown',
): Promise<IPublicTypeProjectSchema> => {
  const pageSchema = await getPageSchema(scenarioName);
  return generateProjectSchema(pageSchema);
};

// 页面
export const getPageSchema = async (scenarioName: string = 'unknown') => {
  const pageSchema = getProjectSchemaFromLocalStorage(scenarioName)?.componentsTree?.[0];
  if (pageSchema) {
    return pageSchema;
  }

  return DefaultPageSchema;
};

export const getPreviewLocale = (scenarioName: string) => {
  const key = getLSName(scenarioName, 'previewLocale');
  return window.localStorage.getItem(key) || 'zh-CN';
};

export const setPreviewLocale = (scenarioName: string, locale: string) => {
  const key = getLSName(scenarioName, 'previewLocale');
  window.localStorage.setItem(key, locale || 'zh-CN');
  window.location.reload();
};

export const getGlobalApp = () => {
  return globalApp;
};
