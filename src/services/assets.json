{"packages": [{"package": "lodash", "library": "_", "urls": ["https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/lodash/4.6.1/lodash.min.js"]}, {"package": "dayjs", "version": "1.11.10", "urls": ["https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/dayjs/1.11.10/dayjs.min.js"], "library": "dayjs"}, {"package": "moment", "version": "2.24.0", "urls": ["https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/moment/2.24.0/moment.min.js"], "library": "moment"}, {"package": "echarts", "version": "5.4.3", "urls": ["https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/echarts/5.4.3/echarts.min.js"], "library": "echarts"}, {"package": "bizcharts", "version": "4.1.23", "urls": ["https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/bizcharts/4.1.23/BizCharts.min.js"], "library": "Bizcharts"}, {"package": "antd", "version": "4.24.13", "urls": ["https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/antd/version/4.24.13/antd.min.js", "https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/antd/version/4.24.13/antd.min.css"], "library": "antd"}, {"title": "fusion组件库", "package": "@alifd/next", "version": "1.26.4", "urls": ["https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/@alifd/next/1.26.4/next.min.css", "https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/@alifd/next/1.26.4/next-with-locales.min.js"], "library": "Next"}, {"title": "NextTable", "package": "NextTable", "version": "1.0.1", "urls": ["https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/fusion-platform/pro-table/1.0.1/next-table.js", "https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/fusion-platform/pro-table/1.0.1/next-table.css"], "library": "NextTable"}, {"package": "@alilc/lowcode-materials", "version": "1.0.7", "library": "AlilcLowcodeMaterials", "urls": ["https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/%40alilc/lowcode-materials/1.0.7/AlilcLowcodeMaterials.js", "https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/%40alilc/lowcode-materials/1.0.7/AlilcLowcodeMaterials.css"], "editUrls": ["https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/%40alilc/lowcode-materials/1.0.7/view.js", "https://bananain-cdn-h5.oss-cn-shenzhen.aliyuncs.com/npm/base/%40alilc/lowcode-materials/1.0.7/view.css"]}, {"package": "@alifd/layout", "version": "2.0.7", "library": "AlifdLayout", "urls": ["https://alifd.alicdn.com/npm/@alifd/layout@2.0.7/dist/AlifdLayout.js", "https://alifd.alicdn.com/npm/@alifd/layout@2.0.7/dist/AlifdLayout.css"], "editUrls": ["https://alifd.alicdn.com/npm/@alifd/layout@2.0.7/build/lowcode/view.js", "https://alifd.alicdn.com/npm/@alifd/layout@2.0.7/build/lowcode/view.css"]}, {"package": "jiaoneiui", "version": "0.0.4", "library": "Jiaoneiui", "urls": ["https://cdn-h5.bananain.cn/npm/JNUI/version/1.0.0/view.js", "https://cdn-h5.bananain.cn/npm/JNUI/version/1.0.0/view.css"], "editUrls": ["https://cdn-h5.bananain.cn/npm/JNUI/version/1.0.0/view.js", "https://cdn-h5.bananain.cn/npm/JNUI/version/1.0.0/view.css"]}], "components": [{"exportName": "JiaoneiuiMeta", "npm": {"package": "jiaoneiui", "version": "0.0.4"}, "url": "https://cdn-h5.bananain.cn/npm/JNUI/version/1.0.0/meta.js", "urls": {"default": "https://cdn-h5.bananain.cn/npm/JNUI/version/1.0.0/meta.js", "design": "https://cdn-h5.bananain.cn/npm/JNUI/version/1.0.0/meta.design.js"}}, {"exportName": "AlilcLowcodeMaterialsMeta", "npm": {"package": "@alilc/lowcode-materials"}, "url": "https://alifd.alicdn.com/npm/@alilc/lowcode-materials@1.0.7/build/lowcode/meta.js", "urls": {"default": "https://alifd.alicdn.com/npm/@alilc/lowcode-materials@1.0.7/build/lowcode/meta.js", "design": "https://alifd.alicdn.com/npm/@alilc/lowcode-materials@1.0.7/build/lowcode/meta.design.js"}}, {"exportName": "AlifdLayoutMeta", "npm": {"package": "@alifd/layout", "version": "2.0.7"}, "url": "https://alifd.alicdn.com/npm/@alifd/layout@2.0.7/build/lowcode/meta.js", "urls": {"default": "https://alifd.alicdn.com/npm/@alifd/layout@2.0.7/build/lowcode/meta.js"}}], "sort": {"groupList": ["蕉内组件", "精选组件", "原子组件"], "categoryList": ["基础元素", "布局容器类", "表格类", "表单详情类", "帮助类", "对话框类", "业务类", "通用", "引导", "信息输入", "信息展示", "信息反馈"]}, "groupList": ["蕉内组件", "精选组件", "原子组件"], "ignoreComponents": {}}