

/**
 * 错误时可选返回，成功时一般必须返回(除非接口明确表示无返回值)
 *
 * PageRO
 */
export interface PageRO {
    /**
     * 应用ID
     */
    appIds?: number[];
    biComponents?: BiComponentRO[];
    biDatasets?: BiDatasetRO[];
    biFilters?: BiFilterRO[];
    biIndicators?: BiIndicatorRO[];
    config?: string;
    /**
     * 创建人
     */
    createBy?: string;
    /**
     * 创建人名称
     */
    createByUsername?: string;
    /**
     * 创建时间
     */
    createTime?: Date;
    datasetIds?: string;
    filterIds?: string;
    /**
     * 筛选器
     */
    filters?: FilterRO[];
    hasCipherKey?: boolean;
    id?: number;
    packages?: string[];
    /**
     * 图标
     */
    pageIcon?: string;
    /**
     * page名称
     */
    pageName?: string;
    /**
     * 发布状态【Unpublished】-未发布，【Published】-已发布
     */
    pageState?: string;
    /**
     * pc=pc页面,mobile=手机页面,为空字符串前端默认为pc类型
     */
    pageType?: string;
    /**
     * 页面权限
     */
    permissions?: PageAuthRO[];
    projectSchema?: any;  // 接口是string 代码中是对象
    /**
     * 更新人
     */
    updateBy?: string;
    /**
     * 更新人名称
     */
    updateByUsername?: string;
    /**
     * 更新时间
     */
    updateTime?: Date;
    /**
     * 校验用户token
     */
    validateUserToken?: boolean;
    [property: string]: any;
}

/**
 * BiComponentRO
 */
export interface BiComponentRO {
    category?: string;
    componentName?: string;
    group?: string;
    title?: string;
    [property: string]: any;
}

/**
 * BiDatasetRO
 */
export interface BiDatasetRO {
    datasetId?: number;
    pageId?: number;
    [property: string]: any;
}

/**
 * BiFilterRO
 */
export interface BiFilterRO {
    filterId?: number;
    pageId?: number;
    [property: string]: any;
}

/**
 * BiIndicatorRO
 */
export interface BiIndicatorRO {
    indicatorId?: string;
    indicatorName?: string;
    [property: string]: any;
}

/**
 * FilterRO
 */
export interface FilterRO {
    /**
     * 观远筛选器ID
     */
    cardId?: string;
    /**
     * 创建人
     */
    createBy?: string;
    /**
     * 创建人名称
     */
    createByUsername?: string;
    /**
     * 创建时间
     */
    createTime?: Date;
    filterMeta?: GuanDataFilterCache;
    /**
     * 筛选器名称
     */
    filterName?: string;
    filterValue?: Result[];
    id?: number;
    pageId?: number;
    treeFilterValue?: Value[];
    /**
     * 更新人
     */
    updateBy?: string;
    /**
     * 更新人名称
     */
    updateByUsername?: string;
    /**
     * 更新时间
     */
    updateTime?: Date;
    withPath?: boolean;
    [property: string]: any;
}

/**
 * GuanDataFilterCache
 */
export interface GuanDataFilterCache {
    cdId?: string;
    cdType?: string;
    content?: Content;
    ctime?: Date;
    name?: string;
    settings?: { [key: string]: any };
    utime?: Date;
    [property: string]: any;
}

/**
 * Content
 */
export interface Content {
    defaultValue?: string;
    filterType?: string;
    options?: string[];
    parameter?: Parameter;
    selectorType?: string;
    source?: { [key: string]: any };
    withPath?: boolean;
    [property: string]: any;
}

/**
 * Parameter
 */
export interface Parameter {
    customize?: boolean;
    defaultValue?: string;
    dpId?: string;
    name?: string;
    optionValue?: string[];
    valueType?: string;
    [property: string]: any;
}

/**
 * Result
 */
export interface Result {
    value?: string;
    [property: string]: any;
}

/**
 * Value
 */
export interface Value {
    children?: Value[];
    key?: string;
    [property: string]: any;
}

/**
 * 页面权限
 */
export interface PageAuthRO {
    /**
     * 用户
     */
    account?: string;
    /**
     * 创建人
     */
    createBy?: string;
    /**
     * 创建人名称
     */
    createByUsername?: string;
    /**
     * 创建时间
     */
    createTime?: Date;
    id?: number;
    /**
     * 用户名
     */
    name?: string;
    pageId?: number;
    /**
     * 权限：edit/read
     */
    permission?: string;
    /**
     * 更新人
     */
    updateBy?: string;
    /**
     * 更新人名称
     */
    updateByUsername?: string;
    /**
     * 更新时间
     */
    updateTime?: Date;
    [property: string]: any;
}


/**
 * 错误时可选返回，成功时一般必须返回(除非接口明确表示无返回值)
 *
 * PageLinkageConfigRO
 */
export interface PageLinkageConfigRO {
    content?: string;
    /**
     * 创建人
     */
    createBy?: string;
    /**
     * 创建人名称
     */
    createByUsername?: string;
    /**
     * 创建时间
     */
    createTime?: Date;
    id?: number;
    pageId?: number;
    /**
     * 更新人
     */
    updateBy?: string;
    /**
     * 更新人名称
     */
    updateByUsername?: string;
    /**
     * 更新时间
     */
    updateTime?: Date;
    [property: string]: any;
}


/**
 * PageView
 * 页面视图
 */
export interface PageView {
    content: string;  // 页面内容
    createBy: string; // 创建人
    createByUsername: string;  // 创建人名称
    createTime: string;
    defaultStatus: number;  //  	默认状态：0 非默认 1 默认
    defaultStatusText: string;
    id: number;
    pageId: number;
    pageViewName: string; // 页面视图名称
    updateBy: string;
    updateByUsername: string;
    updateTime: string;
    viewUid: string;
    viewUserName: string;
    visibleStatus: number; // 可见状态：0 私有 1 公开
    visibleStatusText: string;
    [property: string]: any;
}