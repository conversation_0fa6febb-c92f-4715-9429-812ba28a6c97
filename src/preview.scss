
.edit-docAddon-page{
  padding-top:30px;
}
.edit-module{
  position: relative;
  .checkDom{
    display: none;
  
  }

  &:hover, &.on{
    outline: 2px solid #489CFF !important;
    border-top-left-radius: 0;
    .checkDom{
      position: absolute;
      display: block;
      left: -2px;
      height: 28px;
      min-width: 62px;
      padding: 0 12px;
      line-height: 25px;
      top: -28px;
      color: #fff;
      font-size: 12px;
      background: #489CFF;
      font-size: 12px;
      z-index: 999;
      border-top-right-radius: 8px;
      border-top-left-radius: 8px;
      // box-shadow: 0px 12px 10px 0px rgba(0, 0, 0, 0.03),0px 3px 14px 2px rgba(0, 0, 0, 0.05);
      input{
        float: left;
        margin: 7px 5px 0 0;
        cursor: pointer;
      }
    }
  }
  

}

canvas{
  zoom: 1 !important;
  transform: scale(1) !important;
}