

  type MessageHandler = (message: any) => void;

  class WindowMessenger {
    private targetWindow: Window;
    private targetOrigin: string;
    private messageHandler: MessageHandler;

    constructor(targetWindow: Window, targetOrigin: string) {
      this.targetWindow = targetWindow;
      this.targetOrigin = targetOrigin;

      // 绑定事件处理器
      window.addEventListener('message', this.receiveMessage.bind(this), false);
    }

    // 发送消息
    public sendMessage(message: any): void {
      console.info('===postMessage==', message);
      this.targetWindow.postMessage(message, this.targetOrigin);
    }

    // 设置消息处理器
    public onMessage(messageHandler: MessageHandler): void {
      this.messageHandler = messageHandler;
    }

    // 接收消息
    private receiveMessage(event: MessageEvent): void {
      // 检查消息来源
      if (event.origin !== this.targetOrigin) {
        // console.warn(`Received message from unknown origin: ${event.origin}`);
        return;
      }

      // 调用消息处理器
      if (this.messageHandler) {
        this.messageHandler(event.data);
      }
    }
  }


export const messenger = new WindowMessenger( window.parent, '*');
// messenger.sendMessage('Hello from iframe!');
// 在子窗口（iframe）
