/*
 * @Author: kiki
 * @Date: 2023-10-30 16:39:25
 * @LastEditTime: 2024-01-09 14:56:33
 * @LastEditors: kiki
 * @Description:
 */
import axios from 'axios';
import { getUrlSearchParams } from '../services/utils';
import { messenger } from './postMessage';

// 移除SSO相关的token处理逻辑
// const currUrl = window.location.href;
// const urlToken = getUrlSearchParams('token');
// if (urlToken) {
//   localStorage.setItem('token', urlToken);
//   axios.defaults.headers.common.TOKEN = urlToken;
//   const clearUrl = window.location.href.replace(/[\?&]token=.*/, "");
//   window.history.replaceState(null, '', clearUrl);
// }

// 移除SSO主机配置
// const SSO_HOST_ENV = {
//   dev: 'https://uat.bananain.cn',
//   test: 'https://test-sso.bananain.cn',
//   uat: 'https://uat.bananain.cn',
//   prod: 'https://sso.bananain.cn',
// };
// const SSO_HOST:string = SSO_HOST_ENV[(window?.ENV || 'dev')];

// 移除授权函数
// export const authorize = () => {
//   // SSO自动授权 Url
//   const SSO_AUTH_URL = `${SSO_HOST}/auth-token?rewrite_url=${encodeURIComponent(currUrl)}`;
//   location.replace(SSO_AUTH_URL);
// };

// 简化的请求拦截器，移除token处理
axios.interceptors.request.use(
  (config) => {
    // 移除token相关逻辑
    // const token = localStorage.getItem('token') || '';
    // config.headers.Token = token;
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// 简化的响应拦截器，移除登录错误处理
axios.interceptors.response.use(
  (response) => {
    // 移除SSO相关的错误处理
    // const login_error = ['expired_access_token', 'invalid_access_token', 'deny-anonymous'];
    // const isIframe = window.location.href.includes('iframe');
    // if (login_error.includes(response.data.code)) {
    //   if (isIframe) {
    //     messenger.sendMessage({
    //       eventType: 'Login',
    //     });
    //   } else {
    //      authorize();
    //   }
    // }
    return response.data;
  },
  (error) => {
    return Promise.reject(error);
  },
);
