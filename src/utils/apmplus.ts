/*
 * @Author: kiki
 * @Date: 2023-11-06 15:56:51
 * @LastEditTime: 2023-11-06 16:11:00
 * @LastEditors: kiki
 * @Description: 火山引擎 - APM监控
 */
import browserClient  from '@apmplus/web';
import { getCookie } from './index';

browserClient('init', {
    aid: 540414,
    token: '9c1a82c5f4a4464eb840817af92db1c3',
    userId: getCookie('account_id') || 'guest',
    env: ENV,
    plugins: {
        ajax: {
            collectBodyOnError: true,
        },
        resource: false, // 具体的pluginName可以查看各个插件的配置示例
        performance: false,
        pageview: {
          extractPid: (url) => {
            return new URL(url).searchParams.get('id') || '';
          }
        }
        //integrations: [SPALoadPlugin()], // 手动引入SPALoadPlugin
    },
});
browserClient('start');