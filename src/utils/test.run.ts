/**
 * 脚本批量更新頁面信息
 * @description 测试用例
 */

import axios from "axios";
import { getPageInfo } from "./pageInfo";
import _ from "lodash";
import { Message, Dialog } from '@alifd/next';


const pages = [
    9,
    154,
    127,
    112,
    20,
    53,
    219,
    244,
    85,
    100,
    109,
    178,
    240,
    118,
    430,
    352,
    538,
    549,
    388,
    562,
    20,
    517,
    86,
    87,
    95,
    532,
    358,
    203,
    507,
    564,
    359,
    328,
    355,
    264,
    547,
    277,
    81,
    209,
    203,
    611,
    296,
    306,
    36,
    36,
    334,
    312,
    268,
    349,
    259,
    507,
    329,
    423,
    25,
    374,
    348,
    296,
    296,
    326,
    272,
    393,
    392,
    416,
    563,
    396,
    406,
    431,
    435,
    438,
    439,
    443,
    444,
    445,
    446,
    447,
    407,
    412,
    413,
    414,
    451,
    391,
    455,
    408,
    488,
    453,
    42,
    399,
    526,
    454,
    24,
    507,
    563,
    565,
    565,
    517,
    576,
    579,
    579,
    576,
    584,
    615,
    557,
    511,
    592
]

export const runAllPageInfo = () => {
    pages.forEach(async (id, index) => {
        setTimeout(async () => {
            await updatePageInfo(id)
        }, 500 * index)
       
    })
}


export const updatePageInfo = async (id: string) => {
    const response = await axios.get(`/bjx-app-api/page/detail?id=${id}`);
    if(response.code === 'ok' && response?.result?.projectSchema && !response?.result.biComponents.length) {
        const projectSchema = JSON.parse(response?.result?.projectSchema)
        console.log('---------------------projectSchema------------', projectSchema)
        const componentsTree = _.get(projectSchema, 'componentsTree[0].children');

        const pageInfo = getPageInfo(componentsTree, id)


        const data = {
            id,
            ...pageInfo
          }
        
        
          // TODO:
          const updateSchema = await axios.post('/bjx-app-api/page/update', data)
          if (updateSchema.code === 'ok') {
            Message.success(updateSchema.msg || '成功保存数据');
        
          } else {
            Dialog.confirm({
              v2: true,
              title: "提示",
              content: updateSchema.msg || '保存失败了！',
              okProps: { children: "返回页面列表" },
              cancelProps: { children: "取消" },
              onOk: () => {
                window.location.href = '/polaris-admin/pages'
              }
            });
        }
    }
}