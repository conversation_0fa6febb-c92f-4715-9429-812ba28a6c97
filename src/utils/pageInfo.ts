/**
 * 获取页面的数据信息
 * 1. 统计组件种类
 * 2. 统计指标
 * 3. 统计筛选器
 * 4. 数据集id
 */

import _ from "lodash"

let pageInfo: {
    components: Map<string, any>,
    indicators: Map<number, any>,
    filters:  Map<string, any>,
    bindDataSets: any[]
} = {
    components: new Map(),
    indicators: new Map(),
    filters: new Map(),
    bindDataSets: [],
}

export const getPageData = (pageComponentsTree: any) => {

    pageComponentsTree?.forEach((item: any) => {
        const contentChildren = _.get(item, 'props.content.value')
        if (contentChildren && contentChildren?.length) {
            getPageData(contentChildren)
        }
        // 获取筛选器
        getFiltersInfo(item)

        // 获取指标信息
        getIndicatorInfo(item)

        // 获取组件信息
        getComponentInfo(item)


        if (item.children) {
            getPageData(item.children)
        }

        //针对表格自定义插槽
        if (item.props?.tableContent?.value) {
            getPageData(item.props?.tableContent?.value);
        }
        if (item?.content?.type === 'JSSlot') {
            console.log(item)
            getPageData(item?.content?.value);
        }
        // 针对指标容器
        if (item?.childrenContent?.value) {
            getPageData(item.childrenContent.value);
        }
     
        // 针对下钻容器内
        if (item.componentName === 'JNDrillingContainer') {
            getPageData(item.props?.dataList,);
        }
        // 针对下钻容器内
        if (item.componentName === 'JnIndicatorGroupBox') {
            getPageData(item.props?.slotList);
        }
        // 针对标签页容器
        if (item.componentName === 'JNTabsContainer') {
            
            getPageData(item.props?.tabs,);
        }

    })
    return pageInfo
}

export const getPageInfo = (pageComponentsTree: any, pageId: string) => {
    pageInfo = {
        components: new Map(),
        indicators: new Map(),
        filters: new Map(),
        bindDataSets: [],
    }
    const pageInfoData = getPageData(pageComponentsTree)


    const components = Object.fromEntries(pageInfoData.components);
    const indicators = Object.fromEntries(pageInfoData.indicators);
    const filters =  Object.values(Object.fromEntries(pageInfoData?.filters));
    const bindDataSets = pageInfoData?.bindDataSets

    const biFilters: { filterId: string; pageId: string | number }[] = [];
    filters.forEach((item:any) =>{ const id = item?.id; !['null', null, 'undefined', undefined].includes(id) && biFilters.push({...item, filterId: id, pageId })   } )

    return {
        biComponents: Object.values(components),
        biDatasets: bindDataSets.map((item:any) => ({ ...item, pageId })),
        biFilters,
        biIndicators: Object.values(indicators).map((indicator: any) => ({ indicatorId: indicator.id, indicatorName: indicator.name })),
    }

}

/**
 * 
 * @param filterList 
 */

const getFiltersInfo = (item: any) => {
    // 筛选器容器
    const filterName = ['JNCommonFilterBar', 'JNFilterBar', 'JNSubFilterBar', 'JNAreaTabs', 'JNDisplayFilter', 'JNQuickFilter', 'JNCombinationFilter','JNPolarisFilterBar'];
    //  // 局部筛选器
    //  if (item.componentName === 'JNSubFilterBar') {
    //     if (item?.props?.filters) {
    //         const tempList = item?.props?.filters.map((filter: { filterId: string, effectRanges: any[] }) => {

    //             return filter.filterId + '';
    //         })
    //     }
    // }

    if (filterName.includes(item.componentName)) {
        if (item?.props?.filters) {
            item?.props?.filters.map((filter: { filterId: string, effectRanges: any[], id?: string }) => {
                pageInfo.filters.set(filter?.filterId + '', {...filter, id: filter?.filterId + ''})
                // 20240828 筛选器有数据集的情况
                if(filter?.id){
                    pageInfo.bindDataSets.push( {
                        datasetId:filter.id,
                        nodeId: item.id,
                        componentName:item.componentName,
                        group: "蕉内组件",
                        category: "表格类",
                        title:item.componentName,
                    })
                      
                }
            });
            // 通用组件外面一个是时间filterId 里面还有filters结构， 做下兼容
            if (item.componentName === 'JNCommonFilterBar' && item?.props?.filterId) {
                pageInfo.filters.set(item?.props?.filterId + '', {...item?.props, id: item?.props?.filterId + ''})
            }
        } else if (item?.props?.filterId) {
            pageInfo.filters.set(item?.props?.filterId + '', {...item?.props, id: item?.props?.filterId + ''})
        }
    }
}

/**
 * 获取指标信息
 * 指标列表  JNIndicatorGroupFlex
 * 指标组插槽 JNIndicatorGroupSlot
 * 指标组  JNIndicatorGroup
 * 指标组V2  JNIndicatorGroupV2
 * 4个表格  "ComparedTable", "DynamicColumnTable", "JNTableOfInter", "MixColumnTable",
 * 指标卡-仪表盘 JNDashboardCard
 * 指标卡页签 ComparedCardBox
 * 趋势指标卡 JnLineChartCard
 * 单图达成率 ObjectCardOfSingleGraph
 * 单指标 "ObjectCardOfSingleIndex",
 * 对象指标卡 JNObjectCard
 * 指标卡进度条 JNProgressCard
 * 指标组V3 JNIndicatorGroupV3
 * 
 */

const getIndicatorInfo = (item: any) => {
    // 含有指标容器
    const indicatorBox = [
        "JNIndicatorGroupFlex",
        "JNIndicatorGroupSlot",
        "JNIndicatorGroup",
        "JNIndicatorGroupV2",
        "ComparedTable",
        "DynamicColumnTable",
        "JNTableOfInter",
        "MixColumnTable",
        "JNDashboardCard",
        "ComparedCardBox",
        "JnLineChartCard",
        "ObjectCardOfSingleGraph",
        "ObjectCardOfSingleIndex",
        "JNObjectCard",
        "JNProgressCard",
        "JNIndicatorGroupV3"
        
    ]
    if (indicatorBox.includes(item.componentName)) {
        // 判断是否是表格，  表格类组件都有columns字段
        if (item?.props?.columns) {
            getTableHeaderIndicator(item?.props?.columns)
        }

        // 指标对象 可视化单图
        if (item?.props?.singleGraphCard && item?.props?.singleGraphCard?.columns) {
            getTableHeaderIndicator(item?.props?.singleGraphCard?.columns)
        }

        // 指标卡进度条
        if (item?.props?.mainIndicatorCard) {
            getTableHeaderIndicator(item?.props?.mainIndicatorCard)
        }

        if (item?.props?.progressAreaConfig) {
            getTableHeaderIndicator(item?.props?.progressAreaConfig)
        }
        // 指标对象 主指标
        if (item?.props?.mainIndicatorCard && item?.props?.mainIndicatorCard?.columns) {
            getTableHeaderIndicator(item?.props?.mainIndicatorCard?.columns)
        }
        // 指标对象 页签指标
        if (item?.props?.tabIndicatorCard && item?.props?.tabIndicatorCard?.columns) {
            getTableHeaderIndicator(item?.props?.tabIndicatorCard?.columns)
        }

        // 指标对象 副指标
        if (item?.props?.otherIndicatorCards) {
            getTableHeaderIndicator(item?.props?.otherIndicatorCards)
        }
        // 指组类的对应的字段为
        if (item?.props?.allDataColumn) {
            getTableHeaderIndicator(Object.values(item?.props?.allDataColumn))
        }

        // 指标组V2       
        if (item?.props?.mainData) {
            getTableHeaderIndicator([item?.props?.mainData])
        }

        // 趋势指标卡 数据处理
        if (item.props?.dataColumn) {
            getTableHeaderIndicator(Object.values(item?.props?.dataColumn))
        }

    }
}

/**
 *  表格的标头指标需要通过递归进行获取
 * 
 */
const getTableHeaderIndicator = (columns: any) => {
    if (!_.isArray(columns)) {
        return
    }
    columns.forEach((column: any) => {
        const indicatorId = +(column?.explanation || column?.tooltipText || column?.tooltipBind )
        const name = column?.nickName || column?.title || column?.mainTitle || column?.fieldName || ''
        if (indicatorId) {
            pageInfo.indicators.set(indicatorId, { id: indicatorId, name })
        }
        if (column?.children) {
            getTableHeaderIndicator(column?.children)
        }
        if (column?.assistants) {
            getTableHeaderIndicator(column?.assistants)
        }
        if (column?.columns) {
            getTableHeaderIndicator(column?.columns)
        }
        if (column?.list) {
            getTableHeaderIndicator(column?.list)
        }
        if (column?.['1']) {
            getTableHeaderIndicator(Object.values(column))
        }
        //  指标组里面可能会有图表数据绑定， 这个补全数据集收集
        const chartDataset = column?.chartDataset 
        if (chartDataset) {
            pageInfo.bindDataSets.push(chartDataset)
        }
    })
}

/**
 * 获取组件信息
 */

const getComponentInfo = (item: any) => {
    let componentBaseInfo;
    const componentsMap = _.get(window, 'JiaoneiuiMeta.components')
    // 组件映射表格
    if (!componentsMap) {
        return
    }
    const componentInfo = _.find(componentsMap, { componentName: item.componentName });
    if (componentInfo) {
        componentBaseInfo = _.pick(componentInfo, ['componentName', 'title', 'category', 'group'])
        // console.log('------------- 含有指标容器------------------------',componentBaseInfo)

    }
   
    // 含有指标容器
    if (!pageInfo.components.has(item.componentName) && componentBaseInfo) {
        pageInfo.components.set(item.componentName,
            componentBaseInfo
        )
    }
    // 获取组件的绑定数据集
    const bindDataSet = _.get(item, 'props.id') ||
        _.get(item, 'props.specificDataset.bindDataset') ||
        _.get(item, 'props.bindDataset') ||
        _.get(item, 'props.indicatorGroupDataset.bindDataset');
    // console.log('------------- 组件的绑定数据集------------------------',bindDataSet,  item, componentBaseInfo)
    if (bindDataSet) {
        pageInfo.bindDataSets.push( {
            datasetId: bindDataSet,
            nodeId: item.id,
            ...componentBaseInfo
        })
    }
    


}