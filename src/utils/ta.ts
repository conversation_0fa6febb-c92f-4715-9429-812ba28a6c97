/*
 * @Author: kiki
 * @Date: 2023-12-16 18:09:47
 * @LastEditTime: 2024-01-09 12:58:04
 * @LastEditors: kiki
 * @Description: 数数埋点
 */
import ta from 'thinkingdata-browser';
import { parseJwt } from './jwt';

// 创建 SDK 配置对象
const config = {
  appId:
    window.ENV === 'prod' || window.ENV === 'uat'
      ? 'bffa023939714460b5a160620e64c6a6'
      : 'debug-appid',
  name: 'TA',
  serverUrl: 'https://ta.bananain.cn',
};

ta.init(config);

const isReport = new URLSearchParams(location.search).get('isReport');
const Doc_url = new URLSearchParams(location.search).get('Doc_url');
const Doc_name = new URLSearchParams(location.search).get('Doc_name');
const page_id = new URLSearchParams(location.search).get('id');

window.addEventListener('pageshow', () => {
  if (isReport) {
    // 移除SSO token相关的用户识别逻辑
    // const ssoToken = localStorage.getItem('token');
    // if (ssoToken) {
    //     const { jti, aud } = parseJwt(ssoToken);
    //     ta.userSetOnce({
    //         account_id: jti,
    //         account_name: aud,
    //     });
    //     ta.login(jti);
    // }
    ta.track('pageview', {
      environment: window.ENV.toUpperCase(),
      application: '低码平台',
      url: location.href,
      url_path: location.pathname,
      title: document.title,
    });

    if (Doc_url && Doc_name) {
      ta.track('plugin_embedded_in_doc', {
        environment: window.ENV.toUpperCase(),
        application: '低码平台',
        url: location.href,
        page_id,
        url_path: location.pathname,
        title: document.title,
        Doc_url: `https://fve8bmmwllf.feishu.cn/docx/${Doc_url}`,
        Doc_name: Doc_name,
      });
    }
  }
});
