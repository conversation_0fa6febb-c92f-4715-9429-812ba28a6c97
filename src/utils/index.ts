/*
 * @Author: kiki
 * @Date: 2023-11-06 16:09:16
 * @LastEditTime: 2023-11-06 21:37:45
 * @LastEditors: kiki
 * @Description: 
 */
const parseCookie = (cookie?: string) => {
  return cookie
    ? cookie
      .split(';')
      .map((i) => i.split('='))
      .reduce((acc, [k, v]) => (acc[k.trim().replace('"', '')] = v) && acc, {})
    : {};
};

export const getCookie = (key: string) => {
  // 判断url上有没有parent来确认是否同源
  const sameOrigin = window.location.href.indexOf('parent') != -1;
  const cookie = Object.assign(parseCookie(document.cookie), sameOrigin? {}: parseCookie(window.parent.document.cookie));
  return cookie[key] || '';
};


  /**
   * 如果是手机页面才执行这段代码
   * @param isMobilePage  是否是移动端页面sh
   */
export const setPageGlobalFontSize = (isMobilePage: boolean) => {
  


  function setHtmlFontSize() {
    const htmlEl = document.documentElement;
    const box = document.getElementById('ice-container');
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
    if(!box) {
      return
    }
    if (!isMobile && isMobilePage) {
      box.style.maxWidth = '450px';
      box.style.margin = '0 auto';
    }

    const fontSize = box?.clientWidth / 375 * 50 || 16;
    htmlEl.style.fontSize = fontSize.toFixed(3) + "px";
  }
  setHtmlFontSize();

  window.addEventListener('resize', setHtmlFontSize)
}