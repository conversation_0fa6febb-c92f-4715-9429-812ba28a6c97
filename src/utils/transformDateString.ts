import dayjs, { ManipulateType } from 'dayjs';

const DATE_TYPE = {
  DYNAMIC: 1,
  STATIC: 2,
};

const parseDynamicDate = (dateString: string) => {
  const isComplexDateString = dateString?.includes(';');

  const handleComplexDateString = () => {
    // 此复杂情况的其中一种示例为：'year-1;month:10;day:24;hour:0;minute:0;second:0', 中间的分隔符可能为+、-、:，分别表示时间度量单位增加一位，减少一位，当前单位，需要通过数据处理为 'YYYY-MM-DD' 格式，请帮我封装为通用的方法
    const dateArray = dateString?.split(';');
    // debugger;
    let resultDayjsDate = dayjs();

    dateArray.forEach((item) => {
      // const itemArray = item?.split(/[+\-:]/);
      // const dateUnit: ManipulateType = itemArray?.[0] as ManipulateType;
      // const dateValue: number = itemArray?.[1] as unknown as number;
      if (item?.includes('+') || item?.includes('-') || item?.includes(':')) {
        const dateArray = item?.split(/[+\-:]/);
        const dateUnit: ManipulateType = dateArray?.[0] as ManipulateType;
        const dateValue: number = +dateArray?.[1] as unknown as number;

        if (item?.includes('+')) {
          resultDayjsDate = resultDayjsDate.add(dateValue, dateUnit);
        } else if (item?.includes('-')) {
          resultDayjsDate = resultDayjsDate.subtract(dateValue, dateUnit);
        } else if (item?.includes(':')) {
          if (dateUnit === 'day') {
            resultDayjsDate = resultDayjsDate.date(dateValue);
            return;
          } else if (dateUnit === 'month') {
            resultDayjsDate = resultDayjsDate.month(dateValue - 1);
            return;
          }
          resultDayjsDate = resultDayjsDate[dateUnit as 'day'](dateValue);
        }
      } else {
        resultDayjsDate = resultDayjsDate.startOf(item as ManipulateType);
      }
      // console.log('--------', resultDayjsDate.format('YYYY-MM-DD'));
    });

    const formatDateString = resultDayjsDate.format('YYYY-MM-DD');

    return formatDateString;
  };

  const handleSimpleDateString = () => {
    // 匹配 dateString 中的 "+" "-" ":" ，有则将符号前后分隔，前面作为时间度量单位，后面作为时间增量或者减量或者计算时间结果的单位，需要通过调用 dayjs 第三方库的方法格式化返回'YYYY-MM-DD'格式，无则直接返回
    if (dateString?.includes('+') || dateString?.includes('-') || dateString?.includes(':')) {
      const dateArray = dateString?.split(/[+\-:]/);
      const dateUnit: ManipulateType = dateArray?.[0] as ManipulateType;
      const dateValue: number = dateArray?.[1] as unknown as number;

      if (dateString?.includes('+')) {
        return dayjs().add(dateValue, dateUnit).format('YYYY-MM-DD');
      } else if (dateString?.includes('-')) {
        if (dateUnit === 'week') {
          return dayjs().subtract(dateValue, dateUnit).startOf('week').format('YYYY-MM-DD');
        }
        return dayjs().subtract(dateValue, dateUnit).format('YYYY-MM-DD');
      } else if (dateString?.includes(':')) {
        if (dateUnit === 'day') {
          return dayjs().date(dateValue).format('YYYY-MM-DD');
        } else if (dateUnit === 'month') {
          return dayjs()
            .month(dateValue - 1)
            .format('YYYY-MM-DD');
        }

        return dayjs()[dateUnit](dateValue).format('YYYY-MM-DD');
      }
    } else {
      if (dateString === 'week') {
        const isSameDay =
          dayjs()
            .startOf(dateString as ManipulateType)
            .format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD');

        return isSameDay
          ? dayjs().subtract(1, 'week').format('YYYY-MM-DD')
          : dayjs().startOf(dateString).format('YYYY-MM-DD');
      }

      if (dateString === 'month') {
        const isSameDay =
          dayjs()
            .startOf(dateString as ManipulateType)
            .format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD');

        return isSameDay
          ? dayjs().subtract(1, 'month').format('YYYY-MM-DD')
          : dayjs().startOf(dateString).format('YYYY-MM-DD');
      }

      if (dateString === 'year') {
        const isSameDay =
          dayjs()
            .startOf(dateString as ManipulateType)
            .format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD');

        return isSameDay
          ? dayjs().subtract(1, 'year').format('YYYY-MM-DD')
          : dayjs().startOf(dateString).format('YYYY-MM-DD');
      }

      return dayjs()
        .startOf(dateString as ManipulateType)
        .format('YYYY-MM-DD');
    }
  };

  if (isComplexDateString) {
    const result = handleComplexDateString();
    // console.log('[Dynamic-Complex]', result);
    return result;
  } else {
    const result = handleSimpleDateString();
    // console.log('[Dynamic-Simple]', result);
    return result;
  }
};
const parseStaticDate = (dateString: string) => {
  return dateString;
};

export const transformDateString = (filterItem) => {
  if (filterItem) {
    const parseContent = JSON.parse(filterItem?.content);

    const startDateContent =
      filterItem?.startDateType === DATE_TYPE.DYNAMIC
        ? parseDynamicDate(parseContent?.[0])
        : parseStaticDate(parseContent?.[0]);

    const endDateContent =
      filterItem?.endDateType === DATE_TYPE.DYNAMIC
        ? parseDynamicDate(parseContent?.[1])
        : parseStaticDate(parseContent?.[1]);

    // 备用时间渲染
    const backupParseContent = JSON.parse(filterItem?.backupContent);
    const backStartDate =
      Number(filterItem?.backupStartDateType) === DATE_TYPE.DYNAMIC
        ? parseDynamicDate(backupParseContent?.[0])
        : parseStaticDate(backupParseContent?.[0]);
    const backEndDate =
      Number(filterItem?.backupEndDateType) === DATE_TYPE.DYNAMIC
        ? parseDynamicDate(backupParseContent?.[1])
        : parseStaticDate(backupParseContent?.[1]);

    // 时间范围判定是否正常，正常情况：结束时间 >= 开始时间
    const isRangeCorrect = dayjs(endDateContent).unix() >= dayjs(startDateContent).unix();

    const rangeDate = isRangeCorrect
      ? [startDateContent, endDateContent]
      : [backStartDate, backEndDate];

    return rangeDate;
  }
};
