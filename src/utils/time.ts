import dayjs from 'dayjs';
import { transformDateString } from './transformDateString';
const dateFormat = 'YYYY-MM-DD';

const rangeList = {
    // 昨天
    yesterday: [
        dayjs().subtract(1, 'days').format(dateFormat),
        dayjs().subtract(1, 'days').format(dateFormat),
    ],
    // 近7天
    days7: [
        dayjs().subtract(7, 'days').format(dateFormat),
        dayjs().subtract(1, 'days').format(dateFormat),
    ],
    // 近30天
    days30: [
        dayjs().subtract(30, 'days').format(dateFormat),
        dayjs().subtract(1, 'days').format(dateFormat),
    ],
    // 今年
    year: [dayjs().startOf('year').format(dateFormat), dayjs().endOf('year').format(dateFormat)],
    // 今日
    today: [dayjs().startOf('day').format(dateFormat), dayjs().endOf('day').format(dateFormat)],
    // 上周
    lastWeek: [
        dayjs().subtract(1, 'week').startOf('week').format(dateFormat),
        dayjs().subtract(1, 'week').endOf('week').format(dateFormat),
    ],
    // 本周到昨天
    thisWeekToYesterday: dayjs().startOf('week').format(dateFormat) === dayjs().format(dateFormat)
        ? [
            dayjs().subtract(1, 'week').startOf('week').format(dateFormat),
            dayjs().subtract(1, 'week').endOf('week').format(dateFormat),
        ]
        : [
            dayjs().startOf('week').format(dateFormat),
            dayjs().subtract(1, 'days').format(dateFormat),
        ],
    // 本月到昨天
    thisMonthToYesterday: dayjs().startOf('month').format(dateFormat) === dayjs().format(dateFormat) ?
        [

            dayjs().subtract(1, 'days').startOf('month').format(dateFormat),
            dayjs().subtract(1, 'days').format(dateFormat),
        ] :
        [
            dayjs().startOf('month').format(dateFormat),
            dayjs().subtract(1, 'days').format(dateFormat),
        ],
    // 本年到昨天
    thisYearToYesterday: dayjs().startOf('year').format(dateFormat) === dayjs().format(dateFormat) ? [
        dayjs().subtract(1, 'days').startOf('year').format(dateFormat),
        dayjs().subtract(1, 'days').format(dateFormat),
    ] : [
        dayjs().startOf('year').format(dateFormat),
        dayjs().subtract(1, 'days').format(dateFormat),
    ],
    // 预售到昨天
    presaleToYesterday: [`${dayjs().year()}-10-24`, dayjs().subtract(1, 'days').format(dateFormat)],
    // 正式到昨天
    saleToYesterday: [`${dayjs().year()}-10-31`, dayjs().subtract(1, 'days').format(dateFormat)],
    // 双十一备货
    doubleEleven: [`${dayjs().year()}-10-01`, `${dayjs().year()}-11-11`],
    // 双11预售起
    doubleElevenPresale: [`${dayjs().year()}-10-24`, `${dayjs().year()}-11-11`],
    // 双11正式起
    doubleElevenOfficial: [`${dayjs().year()}-10-31`, `${dayjs().year()}-11-11`],
    // 双12备货
    doubleTwelve: [`${dayjs().year()}-11-12`, `${dayjs().year()}-12-12`],
    // 未来7天
    futureSeven: [
        dayjs().add(1, 'days').format(dateFormat),
        dayjs().add(7, 'days').format(dateFormat),
    ],
    // 未来30天
    futureThirty: [
        dayjs().add(1, 'days').format(dateFormat),
        dayjs().add(30, 'days').format(dateFormat),
    ],
    // 未来90天
    futureNinety: [
        dayjs().add(1, 'days').format(dateFormat),
        dayjs().add(90, 'days').format(dateFormat),
    ],
    // 未来180天
    futureThirtyEighty: [
        dayjs().add(1, 'days').format(dateFormat),
        dayjs().add(180, 'days').format(dateFormat),
    ],
};

export const getDateRangeByKey = (key: string, quickFilterPage?: any[]) => {
    const baseResult =
        rangeList[
        key as
        | 'yesterday'
        | 'days7'
        | 'days30'
        | 'doubleEleven'
        | 'doubleElevenPresale'
        | 'doubleElevenOfficial'
        | 'doubleTwelve'
        | 'futureSeven'
        | 'futureThirty'
        | 'futureNinety'
        | 'futureThirtyEighty'
        ];
    if (!baseResult) {
        if (quickFilterPage && quickFilterPage.length) {
            const matchFilterItem = quickFilterPage.find((item: any) => item.id === key);
            const result = transformDateString(matchFilterItem);
            return result;
        }
    }
    return baseResult;
};

// 处理时分秒筛选器动态默认值
export const handleDateTimeFilterDefaultValue = (key: string, selectorType?: string, filterType?: string) => {
    let timeData: any[] = [];

    // timeFrame:时间范围  singleTime:时间单选 dateTime:时分秒
    switch (key) {
        case 'YESTERDAY': // 昨天
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().subtract(1, 'days').format(dateFormat)}`,
                    `${dayjs().subtract(1, 'days').format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().subtract(1, 'days').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().subtract(1, 'days').format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'LAST_7_DAY': // 最近7天
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().subtract(7, 'days').format(dateFormat)}`,
                    `${dayjs().subtract(1, 'days').format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().subtract(7, 'days').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().subtract(7, 'days').format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'LAST_30_DAY': // 最近30天
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().subtract(30, 'days').format(dateFormat)}`,
                    `${dayjs().subtract(1, 'days').format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().subtract(30, 'days').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().subtract(30, 'days').format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'WEEK_TO_YESTERDAY': // 本周到昨天
            if (selectorType === 'timeFrame') {
                timeData = dayjs().startOf('week').format(dateFormat) === dayjs().format(dateFormat)
                    ? [
                        `${dayjs().subtract(1, 'week').startOf('week').format(dateFormat)}`,
                        `${dayjs().subtract(1, 'week').endOf('week').format(dateFormat)}`,
                    ] :
                    [
                        `${dayjs().startOf('week').format(dateFormat)}`,
                        `${dayjs().subtract(1, 'days').format(dateFormat)}`,
                    ]
            } else if (selectorType === 'singleTime') {
                timeData = dayjs().startOf('week').format(dateFormat) === dayjs().format(dateFormat) ?
                    [`${dayjs().subtract(1, 'week').startOf('week').format(dateFormat)}`,]
                    :
                    [`${dayjs().startOf('week').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = dayjs().startOf('week').format(dateFormat) === dayjs().format(dateFormat) ?
                    [`${dayjs().subtract(1, 'week').startOf('week').format(dateFormat)} 00:00:00`,]
                    :
                    [`${dayjs().startOf('week').format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'MONTH_TO_YESTERDAY': // 本月到昨天
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().startOf('month').format(dateFormat)}`,
                    `${dayjs().subtract(1, 'days').format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().startOf('month').format(dateFormat)}`]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().startOf('month').format(dateFormat)} 00:00:00`]
            }

            break;

        // case 'QUARTER_TO_YESTERDAY': // 本季度到昨天

        //     break;

        case 'YEAR_TO_YESTERDAY': // 本年到昨天
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().startOf('year').format(dateFormat)}`,
                    `${dayjs().subtract(1, 'days').format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().startOf('year').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().startOf('year').format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'TODAY': // 今天
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().format(dateFormat)}`,
                    `${dayjs().format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'DAY_BEFORE_YESTERDAY': // 前天
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().subtract(2, 'days').format(dateFormat)}`,
                    `${dayjs().subtract(2, 'days').format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().subtract(2, 'days').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().subtract(2, 'days').format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'LAST_14_DAY': // 最近14天
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().subtract(14, 'days').format(dateFormat)}`,
                    `${dayjs().subtract(1, 'days').format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().subtract(14, 'days').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().subtract(14, 'days').format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'LAST_90_DAY': // 最近90天
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().subtract(90, 'days').format(dateFormat)}`,
                    `${dayjs().subtract(1, 'days').format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().subtract(90, 'days').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().subtract(90, 'days').format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'LAST_1_YEAR': // 最近1年
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().subtract(1, 'years').format(dateFormat)}`,
                    `${dayjs().subtract(1, 'days').format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().subtract(1, 'years').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().subtract(1, 'years').format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'WEEK_TO_DAY': // 本周到今天
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().startOf('week').format(dateFormat)}`,
                    `${dayjs().format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().startOf('week').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().startOf('week').format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'MONTH_TO_DAY': // 本月到今天
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().startOf('month').format(dateFormat)}`,
                    `${dayjs().format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().startOf('month').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().startOf('month').format(dateFormat)} 00:00:00`,]
            }

            break;

        // case 'QUARTER_TO_DAY': // 本季度到今天

        //     break;

        case 'YEAR_TO_DAY': // 本年到今天
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().startOf('year').format(dateFormat)}`,
                    `${dayjs().format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().startOf('year').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().startOf('year').format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'LAST_WEEK': // 上个自然周
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().subtract(1, 'week').startOf('week').format(dateFormat)}`,
                    `${dayjs().subtract(1, 'week').endOf('week').format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().subtract(1, 'week').startOf('week').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().subtract(1, 'week').startOf('week').format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'WEEK_BEFORE_LAST_WEEK': // 上上个自然周
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().subtract(2, 'week').startOf('week').format(dateFormat)}`,
                    `${dayjs().subtract(2, 'week').endOf('week').format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().subtract(2, 'week').startOf('week').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().subtract(2, 'week').startOf('week').format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'LAST_MONTH': // 上个自然月
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().subtract(1, 'month').startOf('month').format(dateFormat)}`,
                    `${dayjs().subtract(1, 'month').endOf('month').format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().subtract(1, 'month').startOf('month').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().subtract(1, 'month').startOf('month').format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'MONTH_BEFORE_LAST_MONTH': // 上上个自然月
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().subtract(2, 'month').startOf('month').format(dateFormat)}`,
                    `${dayjs().subtract(2, 'month').endOf('month').format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().subtract(2, 'month').startOf('month').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().subtract(2, 'month').startOf('month').format(dateFormat)} 00:00:00`,]
            }

            break;

        case 'YEAR_TO_LAST_MONTH': // 本年到上个自然月
            if (selectorType === 'timeFrame') {
                timeData = [
                    `${dayjs().startOf('year').format(dateFormat)}`,
                    `${dayjs().subtract(1, 'month').endOf('month').format(dateFormat)}`,
                ]
            } else if (selectorType === 'singleTime') {
                timeData = [`${dayjs().startOf('year').format(dateFormat)}`,]
            } else if (selectorType === 'dateTime') {
                timeData = [`${dayjs().startOf('year').format(dateFormat)} 00:00:00`,]
            }
            
            break;

        // case 'YEAR_TO_LAST_QUARTER': // 本年到上个季度

        //     break;

        default:
            break;
    }

    return timeData;
}
