/*
 * @Author: kiki
 * @Date: 2024-01-08 18:42:44
 * @LastEditTime: 2024-01-08 18:44:14
 * @LastEditors: kiki
 * @Description: base64解析
 */
export const parseJwt = (token: string) => {
  try {
      const base64UrlToBase64 = (base64Url: string) => base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const base64ToUtf8 = (base64: string) => decodeURIComponent(escape(atob(base64)));
      const base64UrlToUtf8 = (base64Url: string) => base64ToUtf8(base64UrlToBase64(base64Url));
      const parts = token.split('.');
      const payload = parts[1];
      const decodedPayload = base64UrlToUtf8(payload);
      const jsonPayload = JSON.parse(decodedPayload);
      return jsonPayload;
  } catch (e) {
      console.log('Error parsing JWT:', e);
      return null;
  }
}
// var token = '******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
// var parsed = parseJwt(token);