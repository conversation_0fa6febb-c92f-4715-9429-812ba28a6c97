import { messenger } from "./postMessage"

// 选中的一级模块
let list: string[] = []
let flog = true 

// 回显初始化已经选中的模块
export const intList =  (baseList: string[]) => {
    list = baseList
}

export const addCheckDom = (domNode: Element, schema:any ):void => {
    // domNode?.setAttribute && domNode?.setAttribute('style', 'position: relative;')
    document.querySelector('.lowcode-plugin-sample-preview')?.classList.add('edit-docAddon-page')
    domNode?.classList?.add("edit-module");
    const checkDom = document.createElement('div')
    checkDom?.classList?.add("checkDom")

    // 添加选中按钮
    const checkoutDom = document.createElement('input')
    if(list.includes(schema.id)) {
        checkoutDom.setAttribute('checked', 'checked')
        domNode.classList.add('on')
    }
    checkoutDom.setAttribute('type', 'checkbox')
    checkDom?.appendChild(checkoutDom)
    checkDom?.appendChild(document.createTextNode('选择'))


    // 做事件绑定
    checkoutDom.addEventListener('click', (e:any) => {
        // 这里变更的时候, 放入list中
        if(e.target.checked && schema.id) {
            list.push(schema.id)
            domNode.classList.add('on')
        } else {
            list.splice(list.indexOf(schema.id), 1)
            domNode.classList.remove('on')
        }
          // 埋点数据透传
          messenger.sendMessage({
            eventType: 'pageFilter',
            data: {
                modules:list,
                filters: window.filters,
                timeFiltersType: window?.pageConfig?.timeFiltersType,
            }
          })
    })

    const hasCheckDom = domNode?.querySelector('.checkDom') || null
    if(!hasCheckDom) {
      
      domNode?.appendChild(checkDom)

    }
    // TODO 如果这里是编辑状态，那么我们同时要开放出监听事件
    if(flog){
        window?.JNGlobalEvent?.on('page-global-filter', ()=> {

            messenger.sendMessage({
                eventType: 'pageFilter',
                data: {
                    modules:list,
                    filters: window?.filters,
                    timeFiltersType: window?.pageConfig?.timeFiltersType,
                }
              })
        });
    }
    flog = false
}

