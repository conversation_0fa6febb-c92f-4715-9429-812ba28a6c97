import { IPublicModelPluginContext } from '@alilc/lowcode-types';
import { Button, Select } from '@alifd/next';
import { material } from '@alilc/lowcode-engine';
import { Message } from '@alifd/next';
import { getPageDatasetCategoryList, refreshCache } from 'src/services/dict';
import { event } from '@alilc/lowcode-engine';
// import { runAllPageInfo } from 'src/utils/test.run';
const Option = Select.Option;

const LoadIncrementalAssetsWidgetPlugin = (ctx: IPublicModelPluginContext) => {
  
  return {
    async init() {
      const { skeleton } = ctx;
      const options = await getPageDatasetCategoryList()
      const datasetCategory = localStorage.getItem('datasetCategoryID') || ''

      const loadIncrementalAssets = () => {
        Message.show(  {type: "loading",
        duration:0,
        hasMask:true,
        content:'观远数据源同步中......'});
        refreshCache().then(() => {
          Message.hide();
          material?.onChangeAssets(() => {
            Message.success('观远数据源已同步！');
          });
        })
      }      
      skeleton.add({
        name: 'loadAssetsSample',
        area: 'topArea',
        type: 'Widget',
        props: {
          align: 'right',
          width: 80,
        },
        content: (
          <div>
              数据分类: <Select
              placeholder="show search"
              defaultValue={datasetCategory}
              onChange={(value) => {
                localStorage.setItem('datasetCategoryID', value || '');
                event.emit('JNSelectSetter.bindEvent', value);
              }}
              showSearch
              hasClear
              style={{ width: 150, marginLeft: 5 , marginRight: 8 }}
            >
              {
                (options ||[]).map((option) => <Option value={option.id}>{option.category}</Option> )
              }
             
            </Select>
            <Button onClick={loadIncrementalAssets}>
              同步观远数据源
            </Button>
          </div>
          
        ),
      });
      // skeleton.add({
      //   name: 'loadAssetsSample2',
      //   area: 'topArea',
      //   type: 'Widget',
      //   props: {
      //     align: 'right',
      //     width: 80,
      //   },
      //   content: (
      //     <div>
      //       <Button onClick={() => runAllPageInfo()}>
      //         test
      //       </Button>
      //     </div>
          
      //   ),
      // });
    },
  };
}
LoadIncrementalAssetsWidgetPlugin.pluginName = 'LoadIncrementalAssetsWidgetPlugin';
export default LoadIncrementalAssetsWidgetPlugin;