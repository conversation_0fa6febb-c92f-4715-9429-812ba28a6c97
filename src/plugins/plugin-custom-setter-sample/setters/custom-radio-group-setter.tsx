import React, { useEffect, useState } from 'react';
import { Radio } from '@alifd/next';

const CustomRadioGroupSetter: React.FC = (props) => {
  // Setter-Plugin 提供的 onChange 事件，可通知图层中的组件
  const { onChange, dataSource } = props; // dataSource 由 Setter-Props 定义时传入

  const [currentRadio, setCurrentRadio] = useState('');

  const onRadioChange = (value: string) => {
    setCurrentRadio(value);
    onChange(value);
  };

  useEffect(() => {
    if (props?.value) {
      setCurrentRadio(props?.value);
    }
  }, [props?.value]);

  return (
    <Radio.Group
      size="small"
      direction="hoz"
      value={currentRadio}
      onChange={onRadioChange}
      aria-labelledby="groupId"
    >
      {dataSource.map((item) => {
        return (
          <Radio id={item.value} value={item.value}>
            {item.label}
          </Radio>
        );
      })}
    </Radio.Group>
  );
};

export default CustomRadioGroupSetter;
