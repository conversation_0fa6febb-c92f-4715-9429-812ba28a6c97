import React, { Component } from 'react';
// import classNames from 'classnames';
import { CascaderSelect, Dialog , Button} from "@alifd/next";

interface FilterOption{
  label: string;
  value: string;
  children?: FilterOption[]
}
interface FilterList{
  pageName: string //"首页"
  pgId: string //  "v0b33ddf4e736478e9af0094"
  cards ?: {
    cardName:string //
    cdId: string //
  }[]
}


class CascaderSelectSetter extends Component<any, any> {
  private popupError: (value:string) => void;
  private handleChange: (value: any, data: any, extra: any) => void;
  constructor(props:any) {
    super(props);
    this.popupError = (value:string) => {
      const dialog = Dialog.show({
        v2: true,
        title: 'Error',
        content: <div style={{color:'red'}}>注意！你正在修改已应用的筛选器，会导致原页面筛选失效。如需修改，请删除当前筛选器后再新增。</div>,
        footer: (<>
          <Button warning type="primary" onClick={() => {
            this.props.onChange(value);
            dialog.hide()}}>
              强行修改
          </Button>&nbsp;&nbsp; 
          <Button  type="normal" onClick={() => {
            dialog.hide()}}>
              取消
          </Button></>
        ),
      });
    };
    this.handleChange = (value, data, extra) => {
      let currentPageId =  ''
      let newPageId = ''
      // 获取当前value对应的parent
      for(let i = 0; i < this.state.dataSource.length; i++) {
          for(let j = 0; j < this.state.dataSource[i].children.length; j++) {
            const filter = this.state.dataSource[i].children[j]
            if(filter.value === this.props.value) {
              currentPageId = this.state.dataSource[i].value
            }
            if(filter.value === value) {
              newPageId = this.state.dataSource[i].value
            }
            if(currentPageId && newPageId) {
              break;
            }
          }
      }


      if(this.props.value && currentPageId !== newPageId) {
        this.popupError(value)
      }else{
        this.props.onChange(value);
      }
      

      // const { field, value } = this.props;
      // const propsField = field.parent;
      //     // 获取同级其他属性 showJump 的值
      // const otherValue = propsField.getPropValue('pageId');
      // // set 同级其他属性 showJump 的值
      // propsField.setPropValue('cardId', false);
    };
    this.state = {
      dataSource: []
    };
  }
  bindFunction = () => {
    const { field, value } = this.props;
    const propsField = field.parent;
        // 获取同级其他属性 showJump 的值
    const otherValue = propsField.getPropValue('showJump');
    // set 同级其他属性 showJump 的值
    propsField.setPropValue('showJump', false);
}
  componentDidMount() {
    // fetch("https://os.alipayobjects.com/rmsportal/ODDwqcDFTLAguOvWEolX.json")
    //   .then(response => response.json())
    //   .then(data => this.setState({ data, value: ["2975"] }))
    //   .catch(e => console.log(e));
    // 处理文件getFilterOptions
    // const { pages } = window?.dict && window?.dict?.filterTree && window?.dict?.filterTree[0]
    const pages = _.chain(window?.dict?.filterTree || []).reduce((sum, n) => {
      return [...sum, ...n.pages]
    },[]).map(item => {
      return {
        ...item,
        title: item.pageName,
        value: item.pgId
      }
    }).value()
    const { options } = this.props

    // console.log('===================', pages)
    const buildOptions = (list:FilterList[]) => {
      const backList: FilterOption[] = []
      for(let i = 0; i < list.length; i++) {
        const options:FilterOption = {
          label: list[i].pageName,
          value:list[i].pgId,
          children:[]
        }

        if(list[i]?.cards?.length) {
          for(let j = 0; j < list[i].cards!.length; j ++) {
            if(list[i].cards?.length){
              options.children!.push({
                label: list[i].cards?.[j].cardName || '',
                value: list[i].cards?.[j].cdId || '',
             })
            }
           
          } 
           backList.push(options)

        }
      }
      return backList
    }   

    const dataSource = pages ? buildOptions(pages) : {};

    if(options && options.length) {
      this.setState({ dataSource: options})
    }else{
      this.setState({ dataSource: dataSource})
    }
  
  }


  render() {
    const {  value } = this.props;
    return    <CascaderSelect
    showSearch
    value={value?.value  || value }
    style={{ width: "240px" }}
    dataSource={this.state.dataSource}
    placeholder="搜索名字"
    onChange={(value, data, extra) =>this.handleChange(value, data, extra)}
  />
  }
}

export default CascaderSelectSetter;
