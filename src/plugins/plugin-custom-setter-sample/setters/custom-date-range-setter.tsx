import React, { useState } from 'react';
import { ConfigProvider, DatePicker } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

const CustomDateRangeSetter: React.FC = (props) => {
  // Setter-Plugin 提供的 onChange 事件，可通知图层中的组件
  const { onChange, dataSource } = props; // dataSource 由 Setter-Props 定义时传入

  const onRangePick = (range) => {
    const startDate = dayjs(range[0]).format('YYYY-MM-DD');
    const endDate = dayjs(range[1]).format('YYYY-MM-DD');
    onChange([startDate, endDate]);
  };

  return (
    <>
      <ConfigProvider locale={zhCN}>
        <DatePicker.RangePicker size="small" onChange={onRangePick} />
      </ConfigProvider>
    </>
  );
};

export default CustomDateRangeSetter;
