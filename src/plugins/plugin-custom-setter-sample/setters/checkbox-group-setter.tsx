import React, { useEffect, useState } from 'react';
import { Checkbox } from '@alifd/next';

const CheckboxGroupSetter: React.FC = (props) => {
  // Setter-Plugin 提供的 onChange 事件，可通知图层中的组件
  const { onChange, dataSource } = props; // dataSource 由 Setter-Props 定义时传入

  const [checkedGroup, setCheckedGroup] = useState([]);

  const onCheckboxChange = (checkedValues, options) => {
    setCheckedGroup(checkedValues);
    onChange(checkedValues);
  };

  useEffect(() => {
    if (props?.value?.length) {
      setCheckedGroup(props?.value);
    }
  }, [props?.value]);

  return (
    <>
      <Checkbox.Group dataSource={dataSource} value={checkedGroup} onChange={onCheckboxChange} />
    </>
  );
};

export default CheckboxGroupSetter;
