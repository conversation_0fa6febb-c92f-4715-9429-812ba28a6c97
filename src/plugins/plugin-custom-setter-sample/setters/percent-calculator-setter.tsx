import React, { useEffect, useState } from 'react';
import { Select } from '@alifd/next';

const PercentCalculatorSetter: React.FC = (props) => {
  // Setter-Plugin 提供的 onChange 事件，可通知图层中的组件
  const { onChange, options, ...otherProps } = props; // dataSource 由 Setter-Props 定义时传入

  const [numerator, setNumerator] = useState('');
  const [denominator, setDenominator] = useState('');

  const onNumeratorChange = (val) => {
    setNumerator(val);
    onChange({ numerator: val, denominator });
  };

  const onDenominatorChange = (val) => {
    setDenominator(val);
    onChange({ numerator, denominator: val });
  };

  useEffect(() => {
    if (props?.value && (props?.value?.numerator || props?.value?.denominator)) {
      setNumerator(props?.value?.numerator);
      setDenominator(props?.value?.denominator);
    }
  }, [props?.value?.numerator, props?.value?.denominator])

  return (
    <>
      <div>
        <Select
          style={{ width: 128 }}
          value={numerator}
          dataSource={options}
          placeholder="请选择比例分子"
          autoHighlightFirstItem={false}
          onChange={onNumeratorChange}
        />
        <span style={{margin: '0 8px'}}>/</span>
        <Select
          style={{ width: 128 }}
          value={denominator}
          dataSource={options}
          placeholder="请选择比例分母"
          autoHighlightFirstItem={false}
          onChange={onDenominatorChange}
        />
      </div>
    </>
  );
};

export default PercentCalculatorSetter;
