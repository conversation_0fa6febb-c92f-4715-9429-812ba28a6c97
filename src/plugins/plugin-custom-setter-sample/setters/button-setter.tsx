
import React from 'react';
import { Button } from '@alifd/next';

const ButtonSetter: React.FC = (props) => {
  // Setter-Plugin 提供的 onChange 事件，可通知图层中的组件
  const { onChange } = props; // dataSource 由 Setter-Props 定义时传入


  // useEffect(() => {
  //   if (props?.value?.length) {
  //     setCheckedGroup(props?.value);
  //   }
  // }, [props?.value]);

  return (
    <Button onClick={() => onChange(true)} {...{component: 'button',size: 'small', type:'normal' , ...props}} type="primary">
      {props?.title || 'button'}
    </Button> 
  );
};

export default ButtonSetter;