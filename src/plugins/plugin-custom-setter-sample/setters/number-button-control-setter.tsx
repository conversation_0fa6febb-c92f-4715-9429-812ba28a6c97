import React, { useMemo, useState } from 'react';
import { Row, Button, Input, Space } from 'antd';
import './style/number-button-control-setter.scss';

const NumberControlSetter: React.FC = (props) => {
  // Setter-Plugin 提供的 onChange 事件，可通知图层中的组件
  const { onChange } = props;

  const lengthRange = [2, 6];

  const [numberVal, setNumberVal] = useState(5);

  const handleNumberAdd = () => {
    const nextVal = numberVal + 1;
    setNumberVal(nextVal);
    onChange(nextVal);
  };

  const handleNumberMinus = () => {
    if (numberVal <= lengthRange[0]) {
      return;
    }
    const nextVal = numberVal - 1;
    setNumberVal(nextVal);
    onChange(nextVal);
  };

  const isAddButtonDisabled = useMemo(() => {
    return numberVal >= lengthRange[1];
  }, [numberVal]);

  const isMinusButtonDisabled = useMemo(() => {
    return numberVal <= lengthRange[0];
  }, [numberVal]);

  return (
    <div className="jn-number-control-container">
      <Space size={4}>
        <Button size="small" disabled={isMinusButtonDisabled} onClick={handleNumberMinus}>
          -
        </Button>
        <Input size="small" value={numberVal} />
        <Button size="small" disabled={isAddButtonDisabled} onClick={handleNumberAdd}>
          +
        </Button>
      </Space>
    </div>
  );
};

export default NumberControlSetter;
