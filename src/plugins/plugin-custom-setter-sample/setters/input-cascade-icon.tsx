import React, { useRef, useState } from 'react';
import { Input } from '@alifd/next';
import IconSetter from '@alilc/lowcode-engine-ext/lib/setter/icon-setter';
import { Space } from 'antd';

const InputCascadeIconSetter: React.FC = (props) => {
  // Setter-Plugin 提供的 onChange 事件，可通知图层中的组件
  const { onChange, ...otherProps } = props; // dataSource 由 Setter-Props 定义时传入

  const [text, setText] = useState('');
  const [icon, setIcon] = useState('');

  const onInputChange = (val) => {
    setText(val);
    onChange(val + icon);
  };

  const onIconChange = (icon) => {
    console.log('[Icon]', icon);
    setIcon(icon);
    onChange(text + icon);
  };

  return (
    <>
      <div className="input-cascade-icon-container">
        <Space size={6}>
          <Input placeholder="请输入文字" value={text} onChange={onInputChange} />
          <IconSetter
            {...otherProps}
            type="icon"
            onChange={(icon) => {
              onIconChange(icon);
            }}
          />
        </Space>
      </div>
    </>
  );
};

export default InputCascadeIconSetter;
