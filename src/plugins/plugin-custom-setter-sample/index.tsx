import { IPublicModelPluginContext } from '@alilc/lowcode-types';
import TitleSetter from '@alilc/lowcode-setter-title';
import BehaviorSetter from './setters/behavior-setter';
import CustomSetter from './setters/custom-setter';
import CascaderSelectSetter from './setters/cascader-select-setter';
import NumberControlSetter from './setters/number-button-control-setter';
import CheckboxGroupSetter from './setters/checkbox-group-setter';
import InputCascadeIconSetter from './setters/input-cascade-icon';
import CustomRadioGroupSetter from './setters/custom-radio-group-setter';
import CustomDateRangeSetter from './setters/custom-date-range-setter';
import PercentCalculatorSetter from './setters/percent-calculator-setter';
import ButtonSetter from './setters/button-setter';

// 保存功能示例
const CustomSetterSamplePlugin = (ctx: IPublicModelPluginContext) => {
  return {
    async init() {
      const { setters } = ctx;

      setters.registerSetter('TitleSetter', TitleSetter);
      setters.registerSetter('BehaviorSetter', BehaviorSetter);
      setters.registerSetter('CustomSetter', CustomSetter);
      setters.registerSetter('CascaderSelectSetter', CascaderSelectSetter);
      setters.registerSetter('NumberControlSetter', NumberControlSetter);
      setters.registerSetter('CheckboxGroupSetter', CheckboxGroupSetter);
      setters.registerSetter('InputCascadeIconSetter', InputCascadeIconSetter);
      setters.registerSetter('CustomRadioGroupSetter', CustomRadioGroupSetter);
      setters.registerSetter('CustomDateRangeSetter', CustomDateRangeSetter);
      setters.registerSetter('PercentCalculatorSetter', PercentCalculatorSetter);
      setters.registerSetter('ButtonSetter', ButtonSetter);
    },
  };
};
CustomSetterSamplePlugin.pluginName = 'CustomSetterSamplePlugin';
export default CustomSetterSamplePlugin;
