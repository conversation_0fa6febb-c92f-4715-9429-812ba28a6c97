import { IPublicModelPluginContext, IPublicTypeAssetsJson } from '@alilc/lowcode-types';
import { injectAssets } from '@alilc/lowcode-plugin-inject';
import assetsConfig from '../../services/config'
// import assets from '../../services/assets.json';
import assetsLocal from '../../services/assets.local.json';
import { getProjectSchema, getGlobalApp } from '../../services/pageService';
import _ from 'lodash';


const EditorInitPlugin = (ctx: IPublicModelPluginContext, options: any) => {
  return {
    async init() {
      const { material, project, config } = ctx;
      const scenarioName = options['scenarioName'];
      const scenarioDisplayName = options['displayName'] || scenarioName;
      const scenarioInfo = options['info'] || {};
      // 保存在 config 中用于引擎范围其他插件使用
      config.set('scenarioName', scenarioName);
      config.set('scenarioDisplayName', scenarioDisplayName);
      config.set('scenarioInfo', scenarioInfo);

      // 设置物料描述
      // await material.setAssets(await injectAssets(window?.ENV ==='dev' ? assetsLocal : assets));
      try {
        const ASSETS =  getGlobalApp()?.pageType === 'phone' ? 'ASSETSMOBILE': 'ASSETS'
        // console.log('===========', ASSETS)
        // 将下述链接替换为您的物料即可。无论是通过 utils 从物料中心引入，还是通过其他途径如直接引入物料描述
        const res = window?.ENV && assetsConfig && await fetch(_.get(assetsConfig,`${window?.ENV}.${ASSETS}`) + '?time=' +Date.now()); // 
        const assetsJson = JSON.parse( await res.text()) as unknown as IPublicTypeAssetsJson;

        const injectAssetsJson = window?.ENV ==='dev' ? assetsLocal : assetsJson
        await material.setAssets(await injectAssets(injectAssetsJson));
      } catch (err) {
        console.error(err);
      
      }

      const schema = await getProjectSchema(scenarioName);
      console.log('schema------------------------------------', schema)
      // 加载 schema
      project.importSchema(schema as any);
    },
  };
}
EditorInitPlugin.pluginName = 'EditorInitPlugin';
EditorInitPlugin.meta = {
  preferenceDeclaration: {
    title: '保存插件配置',
    properties: [
      {
        key: 'scenarioName',
        type: 'string',
        description: '用于localstorage存储key',
      },
      {
        key: 'displayName',
        type: 'string',
        description: '用于显示的场景名',
      },
      {
        key: 'info',
        type: 'object',
        description: '用于扩展信息',
      }
    ],
  },
};
export default EditorInitPlugin;